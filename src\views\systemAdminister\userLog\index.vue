<template>
  <div class="open-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px;margin-right: 20px;"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID/昵称"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 150px; margin-right: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ip"
        size="mini"
        placeholder="IP"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.stype"
        placeholder="操作类型"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in typeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="font-size: 12px">操作时间</span>
      <el-date-picker
        style="width: 220px; margin-top: 5px; margin-right: 20px;"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change='filterTimeTransform'
      >
      </el-date-picker>
      <el-input
        v-model="listQuery.device_id"
        size="mini"
        placeholder="设备ID"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-right: 20px; margin-top: 5px"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-right: 20px; margin-top: 5px"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('getuserlogexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        导出
      </el-button>
    </div>


    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="user_id" align="center" min-width="78"> </el-table-column>
      <el-table-column label="用户名" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作类型" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{typeObj[row.op_type]}}</span>
        </template>
      </el-table-column>
      <el-table-column label="IP地址" prop="ip_address" align="center" min-width="130"> </el-table-column>
      <el-table-column label="设备" prop="device" align="center" min-width="120"> </el-table-column>
      <el-table-column label="设备ID" prop="device_id" align="center" min-width="120"> </el-table-column>
      <el-table-column label="操作系统" prop="price" align="center" min-width="90px">
        <template slot-scope="{row}">
          <span>{{os_typeObj[row.os_type]}}</span>
        </template>
      </el-table-column>
      <el-table-column label="语言" prop="price" align="center" min-width="90px">
        <template slot-scope="{row}">
          <span>{{langObj[row.lang_type]}}</span>
        </template>
      </el-table-column>
      <el-table-column label="APP版本" prop="version" align="center" min-width="90px">
        <template slot-scope="{row}">
          <span>{{row.version || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作时间" prop="created_time" align="center" min-width="75"> </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[300,100,50,10]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { getuserlog, getuserlogexport } from "@/api/systemAdminister";
export default {
  name: "userlog",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        ip:"",
        stype: undefined, //账户模式 1：全仓 2：逐仓
        device_id: "", // 设备ID
        pageNo: 1,
        pagesize: 300,
        star: '', //开始
        end: '', //结束
      },
      typeOptions: [
        { key: 0, name: '登录' },
        { key: 1, name: '注册' },
        { key: 2, name: '找回登录密码' },
        { key: 3, name: '设置登录密码' },
        { key: 4, name: '修改登录密码' },
        { key: 5, name: '修改资金密码' },
        { key: 6, name: '修改手机号' },
        { key: 7, name: '修改邮箱' },
        { key: 8, name: '提现申请' },
      ],
      typeObj: {
        0:"登录",
        1:"注册",
        2:"找回登录密码",
        3:"设置登录密码",
        4:"修改登录密码",
        5:"修改资金密码",
        6:"修改手机号",
        7:"修改邮箱",
        8:"提现申请",
        9: "设置资金密码",
        10: "设置谷歌验证器",
        11: "修改谷歌验证器",
        12: "设置手机号",
        13: "设置邮箱",
        14: "KYC1申请",
        15: "KYC2申请",
        16: "人工KYC申请",
      },
      langObj:{
        0: '中文简体',
        1: '英文',
        2: '中文繁体',
        3: '韩语',
        4: '日语',
      },
      os_typeObj:{
        1: "android",
        2: "iOS",
        3: "WEB",
        4: "H5",
        5: "open_api",
        6: "系统自动",
      },
      exportLoading: false, //导出加载中效果
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    this.filterTime = this.timeDefault;
    this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
    this.listQuery.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, {
        stype: this.listQuery.stype === 0 ? 0 : this.listQuery.stype || undefined,
      });
      getuserlog(data).then((res) => {
        this.openList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      getuserlogexport(data).then((res) => {
        if(res.ret == 0){
            window.location.href=res.data.download_url;
            this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
  },
};
</script>
<style lang="scss" scoped>
</style>