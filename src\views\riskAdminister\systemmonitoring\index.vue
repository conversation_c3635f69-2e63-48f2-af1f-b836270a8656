<template>
  <div class="systemmonitoring_wrap">
    <div class="line_wrap">
      <div :class="item.type+'_main'" v-for="(item,index) in contentList" :key="index">
        <el-card :body-style="{ padding: '0px' }">
          <div slot="header" class="top clearfix">
            <h5>{{item.title}}</h5>
          </div>
          <div style="padding: 14px;">
            <div v-if="item.type == 'text'" class="textcontent">
              <span v-if="item.content"><span :style="{color: Number(item.content[0].alarm_level) === 1 ? '#E3170D': ''}">[{{item.content[0].alarm_level}}级]&nbsp;&nbsp;{{item.content[0].content || '--'}}</span><font>{{item.content[0].time && (new Date(item.content[0].time).getTime()/1000).toDate() }}</font></span>
              <span v-else>--</span>
            </div>
            <div class="table_wrap" v-if="item.type == 'table'">
              <ul v-for="(item,index) in item.content" :key="index">
                <li>{{item.contract_code}}</li>
                <li :style="{'color': Number(item.depth_switch) === 4 ? '#FFFFFF' : 'unset','background': Number(item.depth_switch) === 4 ? '#E3170D' : 'unset'}">{{item.depth_switch}}</li>
              </ul>
            </div>
            <div  v-if="item.type == 'text'" class="bottom clearfix">
              <el-button type="text" size="mini" class="button_details" @click="lookAll(item.code,item.title)">查看全部</el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    
    <el-card
      class="kline_card"
      
    >
      <div slot="header" class="clearfix">
        <h5>价格及净持仓监控</h5>
      </div>
      <div class="line_wrap kline_wrap">
        <div>
          <el-card
            class="box_card"
            ref="barparent"
          >
            <ul>
              <li>合约</li>
              <li>买一价</li>
              <li>最新价</li>
              <li>卖一价</li>
            </ul>
            <new-price @pushErrOne="pushErrOne" v-for="(item,index) in contract" :key="index" :contractCode="item.traderpairs"></new-price>
          </el-card>
          <div class="text_main" style="width: 100%; margin-top: 15px;">
            <el-card :body-style="{ padding: '0px' }">
              <div slot="header" class="top clearfix">
                <h5>价格异常报警</h5>
              </div>
              <div style="padding: 14px;">
                <div v-if="KlineErrList[0]" class="textcontent">
                  <span><span :style="{color: Number(KlineErrList[0].alarm_level) === 1 ? '#E3170D': ''}">[{{KlineErrList[0].alarm_level}}级]&nbsp;&nbsp;{{ KlineErrList[0].content }}</span><font>{{ (new Date(KlineErrList[0].time).getTime()/1000).toDate() }}</font></span>
                </div>
                <div v-else>--</div>
                <div class="bottom clearfix">
                  <el-button type="text" size="mini" class="button_details" @click="lookAll('kline','价格异常报警')">查看全部</el-button>
                </div>
              </div>
            </el-card>
          </div>
        </div>
        <div>
          <div :class="item.type+'_main'" v-for="(item,index) in contentListKline" :key="index">
            <el-card :body-style="{ padding: '0px' }">
              <div slot="header" class="top clearfix">
                <h5>{{item.title}}</h5>
              </div>
              <div style="padding: 14px;">
                <div class="textcontent">
                  <span v-if="item.content"><span :style="{color: Number(item.content[0].alarm_level) === 1 ? '#E3170D': ''}">[{{item.content[0].alarm_level}}级]&nbsp;&nbsp;{{item.content[0].content || '--'}}</span><font>{{item.content[0].time && (new Date(item.content[0].time).getTime()/1000).toDate() }}</font></span>
                  <span v-else>--</span>
                </div>
                <div class="bottom clearfix">
                  <el-button type="text" size="mini" class="button_details" @click="lookAll(item.code,item.title)">查看全部</el-button>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </el-card>
     <el-card class="doubleThTable">
      <div slot="header" class="top clearfix viewHeader">
        <h5>{{this.curDate}} 指数稳定观察汇总表</h5>
        <el-button type="text" size="mini" @click="lookYes">查看昨日</el-button>
      </div>
      <el-table 
        v-loading="indexsummaryLoading"
        :data="indexsummaryList"
        border
        fit
        highlight-current-row
      size="mini"
        style="width: 100%;"
        :header-cell-style="{ background: '#F0F8FF' }"
      >
        <el-table-column
          prop="name"
          label=""
          width="95"
        >
        </el-table-column>
      
        <el-table-column 
          v-for="(col,i) in columnList"
          :key="i" 
          :prop="col.prop" 
          :label="col.label"
          align="center">
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 昨日指数稳定观察汇总 -->
    <el-dialog width="1200px" :visible.sync="dialogTableVisible" @closed="handleClose">
      <div slot="title" class="dialog_header">
        <h5>{{yesterday+' 指数稳定观察汇总表'}}</h5>
      </div>
      <el-table 
        :data="yesIndexsummaryList"
        v-loading="yesIndexsummaryLoading"
        border
        fit
        highlight-current-row
      size="mini"
        style="width: 100%;"
        :header-cell-style="{ background: '#F0F8FF' }"
      >
        <el-table-column
          prop="name"
          label=""
          width="95"
        >
        </el-table-column>
      
        <el-table-column 
          v-for="(col,i) in columnList"
          :key="i" 
          :prop="col.prop" 
          :label="col.label"
          align="center">
        </el-table-column>
      </el-table>
    </el-dialog>
    
    <!-- 报警详情 -->
    <el-dialog width="800px" :visible.sync="dialogListVisible" @closed="handleClose">
      <div slot="title" class="dialog_header">
        <h5>{{dialogListTitle}}</h5>
      </div>
      <el-table 
        :data="detailsList"
        v-loading="detailsLoading"
        fit
        highlight-current-row
        height="800"
        style="width: 100%;"
        :header-cell-style="{ background: '#F0F8FF' }"
        size="nimi"
      >
        <el-table-column label="ID" prop="id" align="left" width="100px">
          <template slot-scope="{ row }">
          <span>{{ row.id || '--' }}</span>
        </template>
       </el-table-column>
        <el-table-column label="级别"  prop="alarm_level" align="left" width="50px">
          <template slot-scope="{ row }">
          <span>{{ row.alarm_level || '--' }}</span>
        </template>
       </el-table-column>
        <el-table-column label="内容" prop="content" align="left" min-width="200px">
          <template slot-scope="{ row }">
          <span>{{ row.content || '--' }}</span>
        </template>
       </el-table-column>
        <el-table-column label="时间" prop="time" align="left" min-width="110px">
          <template slot-scope="{ row }">
          <span>{{ parseInt((new Date(row.time)).getTime()/1000).toDate() || '--' }}</span>
        </template>
       </el-table-column>
      </el-table>
    </el-dialog>
    <audio  id="audio">
        <source src="@/assets/music/dingdong.wav" type="audio/ogg">
    </audio>
  </div>
</template>

<script>
//引入封装接口
import {
  systemmonitoring,
  getindexsummary,
  systemmonitoringlist,
} from "@/api/riskAdminister";
import { bcprocontractset } from "@/api/user";
import newPrice from './components/newPrice.vue'

export default {
  name: "systemmonitoring",
  data() {
    return {
      total: 0,
      curDate: null, 
      yesterday: null, 
      indexsummaryLoading: true,
      listLoading: true,
      contentList: [
        {code: 1001, title: 'BTCUSDT合约现货指数采集报警',key: 'SpotIndexMainBTCUSDT',content: undefined,type: 'text'},
        {code: 1002, title: '非BTCUSDT合约现货指数采集报警',key: 'SpotIndexSecondary',content: undefined,type: 'text'},
        {code: 2001, title: '现货指数获取异常报警',key: 'SpotIndexFetchException',content: undefined,type: 'text'},
        {code: 2002, title: '现货指数无法获取报警',key: 'SpotIndexCannotFetch',content: undefined,type: 'text'},
        {code: 2003, title: '最新价格保护报警',key: 'SpotIndexPriceProtect',content: undefined,type: 'text'},
        {code: 0, title: '铺单基准价跟随',key: 'Contrckdepth',content: undefined,type: 'table'},
        {code: 3001, title: '铺单参照系价格获取失败报警',key: 'DepthPriceReferPriceException',content: undefined,type: 'text'},
        {code: 4001, title: '铺单参照系盘口异常报警',key: 'DepthReferExceptio',content: undefined,type: 'text'},
        {code: 5001, title: '快速单边市报警',key: 'FastSideException',content: undefined,type: 'text'},
        {code: 4002, title: '净持仓异常报警',key: 'DepthNetPosException',content: undefined,type: 'text'},
        {code: 5002, title: '持续冲击报警',key: 'ContinueException',content: undefined,type: 'text'},
        {code: 6001, title: '现货指数计算异常记录日志',key: 'SpotExceptionRecord',content: undefined,type: 'text'},
      ],
      contentListKline: [
        {code: 7001, title: 'k线无法获取报警',key: 'KilneExceptionRecord',content: undefined, type: 'text', },
        {code: 7002, title: 'k线成交数据异常',key: 'KilneDataException',content: undefined, type: 'text', },
        {code: 8001, title: '账户异常亏损',key: 'FinceAccountException',content: undefined, type: 'text', },
        {code: 8002, title: '净持仓亏损',key: 'ContractCodeException',content: undefined, type: 'text', },
      ],
      resultData: {},

      columnList: [
        {
          prop: "huobi",
          label: 'huobi'
        },
        {
          prop: "binance",
          label: 'binance'
        },
        {
          prop: "okex",
          label: 'okex'
        },
        {
          prop: "bitfinex",
          label: 'bitfinex'
        },
        {
          prop: "bittrex",
          label: 'bittrex'
        },
        {
          prop: "poloniex",
          label: 'poloniex'
        },
        {
          prop: "coinbase",
          label: 'coinbase'
        },
        {
          prop: "hitBtc",
          label: 'hitBtc'
        },
        {
          prop: "kraken",
          label: 'kraken'
        },
        {
          prop: "bitstamp",
          label: 'bitstamp'
        },
        {
          prop: "ftx",
          label: 'ftx'
        },
      ],
      indexsummaryList: [
        // {
        //   name: '--',
        //   huobi: '0',
        //   binance: '0',
        //   okex: '0',
        //   bitfinex: '0',
        //   bittrex: '0',
        //   poloniex: '0',
        //   coinbase: '0',
        //   hitBtc: '0',
        //   kraken: '0',
        //   bitstamp: '0',
        //   ftx: '0',
        // },
      ],
      // 昨日指数稳定汇总表
      dialogTableVisible: false,  // 弹出框
      yesIndexsummaryList: [], // 列表
      yesIndexsummaryLoading: false,  // 列表加载状态
      // 报警详情列表
      dialogListTitle: '', // 报警详情弹框标题
      dialogListVisible:false, // 弹出框
      detailsList: [], // 列表
      detailsLoading: false, // 列表加载状态

      contract: [],
      
      timer: null, 

      KlineErrList: [
      ],
    };
  },

  components: {
    newPrice,
  },

  computed: {},

  mounted() {
    this.getList();
    this.timer = setInterval(() => {
      this.getList();
    }, 60000);
    this.curDate = parseInt(new Date().getTime()/1000).toDate('yyyy-MM-dd')
	  this.yesterday = this.getDay(-1, '-');		//    -1 代表前一天，-2前两天...
    this.getindexsummary(this.curDate)
    bcprocontractset({}).then((res) => {
      this.contract = res.data.filter(v=>v.isshow == 1).reverse()
    })
  },

  methods: {
    pushErrOne(obj){
      this.KlineErrList.unshift(obj)
    },
    // 查看全部
    lookAll(stype,title){
      this.detailsList = []
      this.dialogListTitle = title
      this.dialogListVisible = true
      if(stype == 'kline'){
        this.detailsList = this.KlineErrList
      }else{
        this.detailsLoading = true
        systemmonitoringlist({stype}).then((res) => {
          this.detailsList = res.data
          this.detailsLoading = false;
        });
      }
    },
    // 查看昨日
    lookYes(){
      this.dialogTableVisible = true
      this.getYesindexsummary(this.yesterday)
    },
    //  table列表数据
    getList() {
      var myVideo = document.getElementById("audio");
      //开始有加载中效果
      this.listLoading = true;
      systemmonitoring().then((res) => {
        let dataObj = res.data;
        this.contentList = this.contentList.map((v,i)=>{
          v.content = dataObj[v.key]
          if(dataObj[v.key] && dataObj[v.key][0].alarm_level && dataObj[v.key][0].alarm_level === 1){
            myVideo.play();
          }
          return v
        })
        this.listLoading = false;
      });
    },
    getindexsummary(datime){
      //开始有加载中效果
      this.indexsummaryLoading = true;
      getindexsummary({datime}).then((res) => {
        let resData = res.data
        for (const key in resData) {
          if (Object.hasOwnProperty.call(resData, key)) {
            const element = resData[key];
            let obj = {}
            obj.name = key
            obj.huobi = element.huobi || '无',
            obj.binance = element.binance || '无',
            obj.okex = element.okex || '无',
            obj.bitfinex = element.bitfinex || '无',
            obj.bittrex = element.bittrex || '无',
            obj.poloniex = element.poloniex || '无',
            obj.coinbase = element.coinbase || '无',
            obj.hitBtc = element.hitBtc || '无',
            obj.kraken = element.kraken || '无',
            obj.bitstamp = element.bitstamp || '无',
            obj.ftx = element.ftx || '无',
            this.indexsummaryList.push(obj)
          }
        }
        this.indexsummaryLoading = false;
      });
    },
    getYesindexsummary(datime){
      //开始有加载中效果
      this.yesIndexsummaryLoading = true;
      getindexsummary({datime}).then((res) => {
        let resData = res.data
        for (const key in resData) {
          if (Object.hasOwnProperty.call(resData, key)) {
            const element = resData[key];
            let obj = {}
            obj.name = key
            obj.huobi = element.huobi || '无',
            obj.binance = element.binance || '无',
            obj.okex = element.okex || '无',
            obj.bitfinex = element.bitfinex || '无',
            obj.bittrex = element.bittrex || '无',
            obj.poloniex = element.poloniex || '无',
            obj.coinbase = element.coinbase || '无',
            obj.hitBtc = element.hitBtc || '无',
            obj.kraken = element.kraken || '无',
            obj.bitstamp = element.bitstamp || '无',
            obj.ftx = element.ftx || '无',
            this.yesIndexsummaryList.push(obj)
          }
        }
        this.yesIndexsummaryLoading = false;
      });
    },
    handleClose(done) {
      this.yesIndexsummaryList = []
    }, 
    // 获取日期
    getDay(num, str) {
        var today = new Date();
        var nowTime = today.getTime();
        var ms = 24*3600*1000*num;
        today.setTime(parseInt(nowTime + ms));
        var oYear = today.getFullYear();
        var oMoth = (today.getMonth() + 1).toString();
        if (oMoth.length <= 1) oMoth = '0' + oMoth;
        var oDay = today.getDate().toString();
        if (oDay.length <= 1) oDay = '0' + oDay;
        return oYear + str + oMoth + str + oDay;
    },
  },
  beforeDestroy() {
    clearInterval(this.timer);
  }
};
</script>
<style lang="scss">
.systemmonitoring_wrap {
  .el-dialog__header{
    display: flex;
    align-items: flex-start;
  }
  .el-dialog__body{
    padding-top: 10px;
  }
}
</style>
<style lang="scss" scoped>
.systemmonitoring_wrap {
  padding-bottom: 20px;
  .line_wrap{
    padding-top: 10px;
    display: flex;
    flex-wrap: wrap;
    &>div{
      margin-top: 15px;
      &:nth-of-type(even){
        margin-left: 15px;
      }
    }
    .text_main{
      width: calc(50% - 8px);
    }
    .table_main{
      width: 100%;
      margin-left: 0 !important;
    }
    .textcontent{
      font-size: 13px;
      line-height: 16px;
      font{
        white-space: nowrap;
        color: #999;
      }
      &>span>span{
        padding-right: 20px;
      }
    }
    .table_wrap{
      border-right: 1px solid #EBEEF5;
      border-bottom: 1px solid #EBEEF5;
      font-size: 12px;
      display: flex;
      width: 100%;
      ul{
        list-style: none;
        padding: 0;
        margin: 0;
        width: 100%;
        li{
          text-align: center;
          height: 36px;
          line-height: 36px;
          border-top: 1px solid #EBEEF5;
          border-left: 1px solid #EBEEF5;
        }
        &>li:first-of-type{
          background: rgb(240, 248, 255);
          color: #909399;
          font-weight: 500;
        }
        &>li:last-of-type{
          color: #000;
        }
      }
      
    }
  }
  .kline_card{
    margin-top: 20px;
    h5{
      margin: 0;
      padding: 0;
    }
  }
  .kline_wrap{
    padding-top: 0;
    &>div{
      width: 50%;
      margin-top: 0;
      &:first-of-type{
        width: calc(50% - 8px);
      }
      &:nth-of-type(even){
        margin-left: 0;
        display: flex;
        justify-content: flex-start;
        flex-direction: column;
        align-items: flex-end;
        padding-left: 8px;
      }
    }
    .box_card {
      .el-card__body {
        padding: 10px;
      }
      ::v-deep ul{
        display: flex;
        list-style: none;    /*清除列表默认样式*/
        padding: 0;      /*清除padding*/
        margin: 0;
        &:first-of-type{
          border-top: 1px solid #EBEEF5;
          background: rgb(240, 248, 255);
          font-weight: bold;
        }
        li{
          border-left: 1px solid #EBEEF5;
          border-bottom: 1px solid #EBEEF5;
          width: 25%;
          height: 23px;
          line-height: 23px;
          text-align: center;
          &:last-of-type{
            border-right: 1px solid #EBEEF5;
          }
        }
      }
    }
    .text_main{
      margin-top: 0;
      margin-bottom: 15px;
      width: calc(100% - 8px);
    }
  }
  .top {
    h5{
      padding: 0;
      margin: 0;
    }
  }
  .bottom {
    margin-top: -16px;
    line-height: 12px;
  }

  .button_details {
    padding: 0;
    float: right !important;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }
  
  .clearfix:after {
      clear: both
  }
  .doubleThTable{
    margin-top: 20px;
    .viewHeader{
      display: flex;
      width: 100% !important;
      align-items: center;
      h5{
        width: 100%;
      }
    }
  }
  .dialog_header{
    display: flex;
    align-items: flex-start;
    h5{
      padding: 0;
      margin: 0;
    }
  }
}
</style>