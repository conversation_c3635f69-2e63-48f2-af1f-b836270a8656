<template>
  <div class="plan-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        clearable
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID/昵称"
        clearable
        style="width: 130px; margin-right: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        clearable
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <!--  -->
      <!-- <div class="block"> -->
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        placeholder="合约"
        clearable
        style="width: 120px; margin-right: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <!-- <el-select
        size="mini"
        v-model="listQuery.account_type"
        placeholder="仓位类型"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select> -->
      <el-select
        size="mini"
        v-model="listQuery.side"
        placeholder="方向"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-input
        v-model="listQuery.ip_address"
        size="mini"
        placeholder="IP"
        clearable
        style="width: 130px; margin-right: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        style="margin-right: 20px;"
        type="primary"
        @click="handleFilter"
      >
        <!-- @click="handleFilter" -->
        搜索
      </el-button>
      <!-- <el-button
        class="filter-item"
        :loading="downloadLoading"
        @click="handleDownload"
        size="mini"
        type="success"
      >
        导出
      </el-button> -->
    </div>

    <el-table
      v-loading="listLoading"
      :data="holdList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column label="用户名" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="合约" prop="contractcode" min-width="90px" align="center"></el-table-column>
      <!-- <el-table-column label="仓位类型" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.account_type==3?'跟单':'逐仓'}}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="方向" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?'卖出开空':'买入开多'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="杠杆" align="center" min-width="60px">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="amount" align="center" min-width="110px">
          <template slot-scope="{ row }">
          <span>{{ row.amount }} 张</span>
        </template>
       </el-table-column>
      <el-table-column label="触发价" prop="trigger_price" align="center" min-width="90px"> 
        <template slot-scope="{ row }">
          <span>{{ row.trigger_price || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" prop="create_time" align="center" width="75">
        <template slot-scope="{ row }">
          <span>{{row.create_time == "0"? '--' : row.create_time }}</span>
        </template>
      </el-table-column>
      <el-table-column label="触发时间" prop="order_time" align="center" width="75">
        <template slot-scope="{ row }">
          <span>{{row.order_time == "0"? '--' : row.order_time}}</span>
        </template>
      </el-table-column>
      <el-table-column label="取消时间" prop="cancel_time" align="center" width="75">
        <template slot-scope="{ row }">
          <span>{{row.cancel_time == "0"? '--' : row.cancel_time}}</span>
        </template>
      </el-table-column>
      <el-table-column label="触发状态" prop="status" align="center" min-width="110px">
          <template slot-scope="{ row }">
          <span>{{ statusObj[row.status] }}</span>
        </template>
       </el-table-column>
      <el-table-column label="计划单ID" prop="plan_order_id" align="center" min-width="78"> </el-table-column>
      <el-table-column label="委托客户IP" prop="ip_address" align="center" min-width="130px"></el-table-column>
      <el-table-column label="设备识别码" prop="imei" align="center" min-width="110px"></el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
//引入的自定义指令
import waves from "@/directive/waves";
//引入封装接口
import { getplanorder } from "@/api/transactionQuery";
import { bcprocontractset } from "@/api/user";

export default {
  name: "followplanQuery",
  data() {
    return {
      listLoading: false,
      total: 0,
      holdList: null,
      contractOptions: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        // account_type: null, //账户模式 1：全仓 2：逐仓
        account_type: 3, //账户模式 3跟单
        contract_code: "", //合约代码
        side: null, //方向 B买S卖
        status: undefined, //状态 1: 未触发 0：取消 2：已触发 3: 触发失败
        ip_address: '',
        pageNo: 1,
        pagesize: 10,
      },
      accountTypeOptions: [
        { key: 1, name: '全仓' },
        { key: 2, name: '逐仓' },
      ],
      sizeOptions: [
        { key: "B", name: '买入开多' },
        { key: "S", name: '卖出开空' },
      ],
      statusOptions: [
        { key: 0, name: '已取消' },
        { key: 1, name: '未触发' },
        { key: 2, name: '已触发' },
        { key: 3, name: '触发失败' },
        // { key: 4, name: '平仓撤销' },
        { key: 5, name: '平仓撤销' },
      ],
      stypeOptions: [
        { key: 2, name: '止盈单' },
        { key: 3, name: '止损单' },
      ],
      
      statusObj: { 
        0: '已取消',
        1: '未触发',
        2: '已触发',
        3: '触发失败',
      },
      downloadLoading: false,//导出加载中效果
    };
  },

  components: {},

  computed: {},

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.getList();
  },

  methods: {
    //  请求表格数据
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, {
        status: this.listQuery.status === 0 ? 0 : this.listQuery.status || -1,
        side: this.listQuery.side || undefined,
        account_type: this.listQuery.account_type || undefined,
      });
      getplanorder(data).then((response) => {
        this.holdList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //表格导出功能
    handleDownload() {
      this.downloadLoading = true;
      import("@/vendor/Export2Excel").then((excel) => {
        const tHeader = [
          "UID",
          "用户名",
          "顶级代理ID",
          "上级代理ID",
          "上机代理用户名",
          "合约",
          "仓位类型",
          "杠杆",
          "张数",
          "冻结保证金",
          "开仓均价",
          "手续费",
          "成交时间",
          "资金费用",
          "强平价格",
          "止盈价",
          "止损价",
          "浮动PNL",
          "IP地址",
        ];
        const filterVal = [
          "uid",
          "username",
          "top_id",
          "Superior_id",
          "top_username",
          "contract_type",
          "position_type",
          "lever",
          "sheets_number",
          "freeze_margin",
          "open_price",
          "handling_fee",
          "transaction_time",
          "funding_costs",
          "liquidation_price",
          "surplus_price",
          "damage_price",
          "float_profit_loss",
          "ip",
        ];
        const data = this.formatJson(filterVal, this.holdList);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: "table-list",
        });
        this.downloadLoading = false;
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          if (j === "transaction_time") {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
  },
};
</script>
<style lang="scss" scoped>
</style>