<template>
  <div class="gold-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.userid"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.groupid"
        placeholder="风控组别"
        clearable
        style="width: 150px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in grouplist"
          :key="item.id"
          :label="item.group_name"
          :value="item.id"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.gaction"
        placeholder="动作类型"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="(val, key, idx) in gactionOptions"
          :key="idx"
          :label="val"
          :value="key"
        />
      </el-select>
      <span style="margin-left: 20px; font-size: 12px">时间</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change='filterTimeTransform'
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin: 10px 0 0 20px;"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="goldList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column 
        label="UID" 
        prop="user_id" 
        align="center" 
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.user_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column 
        label="用户名" 
        prop="user_name" 
        align="center" 
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.user_name || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column 
        label="动作类型" 
        prop="action" 
        align="center" 
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.action === 1 ? '添加' : '剔除'}}</span>
        </template>
      </el-table-column>
      <el-table-column 
        label="风控组别" 
        prop="group_name" 
        align="center" 
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.group_name || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column 
        label="时间" 
        prop="creat_time" 
        align="center" 
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.creat_time || "--" }}</span>
        </template>
      </el-table-column>
     </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  getristlist,
  getgrouplist,
} from "@/api/riskAdminister";

export default {
  name: "riskuserhistory",
  data() {
    return {
      listLoading: false,
      total: 0,
      goldList: null,
      filterTime: [],
      listQuery: {
        userid: "", 
        groupid: "", 
        star: "", //开始
        end: "", //结束
        pageNo: 1,
        pagesize: 10,
      },
      grouplist: [],
      gactionOptions: {
        0: '剔除',
        1: '正常',
      },
      downloadLoading: false,
    };
  },

  components: {},

  computed: {},

  mounted() {
    getgrouplist({pageNo: 1,pagesize: 10000 }).then(res=>{
      this.grouplist = res.data.list
    })
    this.getList();
  },

  methods: {
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      data.userid = data.userid || undefined
      data.groupid = data.groupid || undefined
      data.gaction = data.gaction || -1
      getristlist(data).then((res) => {
        this.goldList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val && val[1]+' 23:59:59' || '';
    },
  },
};
</script>
<style lang="scss" scoped>
</style>