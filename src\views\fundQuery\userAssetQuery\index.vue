<template>
  <div class="asset-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID/顶级昵称"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        style="margin-top: 10px; margin-left: 10px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('getwellinfoexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        导出
      </el-button>
    </div>
     

    <el-table
      v-loading="listLoading"
      :data="assetList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column label="用户名" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总入金" prop="cashin" min-width="80px" align="center"></el-table-column>
      <el-table-column label="总出金" prop="cashout" min-width="80px" align="center"></el-table-column>
      <el-table-column label="净入金" prop="cashnet" min-width="80px" align="center"></el-table-column>
      <el-table-column label="资产账户权益" prop="totalbalance" min-width="105px" align="center"></el-table-column>
      <el-table-column label="资产账户可用" prop="balance" min-width="115" align="center"></el-table-column>
      <el-table-column label="资产账户冻结" prop="withdraw_lock" min-width="100px" align="center"></el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200,100,50,10]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 封装api
import { getwellinfo, getwellinfoexport } from "@/api/fundQuery";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "userAssetQuery",
  data() {
    return {
      listLoading: false,
      total: 0,
      assetList: null,
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        pageNo: 1,
        pagesize: 10,
      },
      exportLoading: false, //导出加载中效果
    };
  },
  components: {},

  computed: {},

  mounted() {
    this.getList();
  },
  methods: {
    //  渲染table列表
    getList() {
      this.listLoading = true;
      getwellinfo(this.listQuery).then((res) => {
        this.assetList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      getwellinfoexport(data).then((res) => {
        if(res.ret == 0){
            // window.location.href=res.data.download_url;
          this.$notify.success({title:'操作成功',message:"请到‘交易查询/导出下载列表’进行下载"})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },

  },
};
</script>
<style lang="scss" scoped>
.asset-container{
  .filter-container{
    .highSwitch_wrap{
      margin-top: 15px; 
      width: 100px; 
      cursor: pointer;
      font-size: 14px;
    }
  }
  .high_filter_wrap {
    width: 100%;
    &::v-deep .el-col{
      display: flex;
      flex-wrap: wrap;
    }
    .filter_item_wrap{
      white-space: nowrap;
      margin: 10px 15px 0 0 ;
    }
    .high_filter_key {
      font-size: 14px;
      margin-right: 10px;
    }
    .high_filter_btn_wrap{
      margin: 10px 0 0 0 ;
    }
  }
  .select_wrap{
    // padding: 0 30px 0 30px;
    display: flex;
    align-items: center;
    span{
      width:100px;
      // padding-right: 20px;
    }
  }
}
</style>