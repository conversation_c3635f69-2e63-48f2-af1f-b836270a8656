<template>
  <div class="asset-container">
    <div class="filter-container">
      <el-row>
        <el-col :span="18">
          <el-input
            size="mini"
            v-model="listQuery.sname"
            placeholder="UID/手机/邮箱"
            style="width: 150px"
            class="filter-item"
            clearable
            @keyup.enter.native="handleFilter"
          />
          <el-input
            v-model="listQuery.sectop"
            size="mini"
            placeholder="顶级代理ID/顶级昵称"
            style="width: 180px; margin-left: 20px; margin-top: 10px"
            class="filter-item"
            clearable
            @keyup.enter.native="handleFilter"
          />
          <!-- <el-input
            v-model="listQuery.sectop"
            size="mini"
            placeholder="顶级昵称"
            style="width: 150px; margin-left: 20px; margin-top: 10px"
            class="filter-item"
            clearable
            @keyup.enter.native="handleFilter"
          /> -->
          <el-input
            v-model="listQuery.sagent"
            size="mini"
            placeholder="上级代理ID/用户名"
            style="width: 180px; margin-left: 20px; margin-top: 10px"
            class="filter-item"
            clearable
            @keyup.enter.native="handleFilter"
          />
          <el-button
            style="margin-top: 10px; margin-left: 10px"
            class="filter-item"
            size="mini"
            type="primary"
            @click="handleFilter"
          >
            搜索
          </el-button>
          <el-button
            class="filter-item"
            :loading="exportLoading"
            v-if="$store.getters.roles.indexOf('getaccinfoexport')>-1"
            @click="handleExport"
            size="mini"
            type="success"
          >
            导出
          </el-button>
        </el-col>
        <el-col :span="6" style="display:flex;justify-content: flex-end;" v-if="$store.getters.roles.indexOf('advancedsearch')>-1">
          <div class="highSwitch_wrap" @click="highFilterCheckout()">
            高级筛选
            <i :class="`el-icon-arrow-${filters?'up':'down'}`"></i>
          </div>
        </el-col>
      </el-row>

      <div class="high_filter_wrap" v-show="filters">
        <el-row>
          <el-col :span="24">
            <div class="filter_item_wrap">
              <span class="high_filter_key">合约净入金区间</span>
              <el-input
                onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
                v-model="listQuery.incashmin"
                style="width:100px;"
                size="mini"
                clearable
              />
              <!--onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"-->
              -
              <el-input
                onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
                style="width: 100px"
                v-model="listQuery.incashmax"
                size="mini"
                clearable
              />
            </div>
            <div class="filter_item_wrap">
              <span class="high_filter_key">平仓笔数区间</span>
              <el-input
                onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
                v-model="listQuery.closmin"
                style="width: 100px;"
                size="mini"
                clearable
              />
              -
              <el-input
                onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
                style="width: 100px"
                v-model="listQuery.closmax"
                size="mini"
                clearable
              />
            </div>
            <div class="filter_item_wrap">
              <span class="high_filter_key">盈亏区间</span>
              <el-input
                onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
                v-model="listQuery.marmin"
                style="width: 100px;"
                size="mini"
                clearable
              />
              -
              <el-input
                onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
                style="width: 100px"
                v-model="listQuery.marmax"
                size="mini"
                clearable
              />
            </div>
            <div class="filter_item_wrap">
              <span class="high_filter_key">手续费</span>
              <el-input
                onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
                v-model="listQuery.commismin"
                style="width: 100px;"
                size="mini"
                clearable
              />
              -
              <el-input
                onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
                style="width: 100px"
                size="mini"
                v-model="listQuery.commismax"
                clearable
              />
            </div>
            <div class="filter_item_wrap">
              <span class="high_filter_key">当前合约权益</span>
              <el-input
                onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
                v-model="listQuery.rightsmin"
                style="width: 100px;"
                size="mini"
                clearable
              />
              -
              <el-input
                onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
                style="width: 100px"
                v-model="listQuery.rightsmax"
                size="mini"
                clearable
              />
            </div>
            <div class="high_filter_btn_wrap">
              <el-button type="success" @click="handleFilter" size="mini">高级筛选</el-button>
             
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
     

    <el-table
      v-loading="listLoading"
      :data="assetList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column label="用户名" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="平仓次数" prop="closcount" align="center" min-width="100px"> </el-table-column>
      <el-table-column label="总入金" prop="totalincash" min-width="80px" align="center"></el-table-column>
      <el-table-column label="总出金" prop="totaloutcash" min-width="80px" align="center"></el-table-column>
      <el-table-column label="净入金" prop="netcash" min-width="80px" align="center"></el-table-column>
      <!-- <el-table-column label="资产账户" prop="welletbace" min-width="80px" align="center"></el-table-column> -->
      <el-table-column label="可用" prop="available" min-width="115" align="center"></el-table-column>
      <el-table-column label="冻结保证金" prop="lockbond" min-width="100px" align="center">
      </el-table-column>
      <el-table-column label="账户权益" prop="rights" min-width="105px" align="center"></el-table-column>
      <el-table-column label="资金费用" prop="capital" min-width="105px" align="center"></el-table-column>
      <el-table-column label="手续费" prop="commission" align="center" min-width="110px"> </el-table-column>
      <el-table-column label="浮动PNL" prop="ripplepnl" align="center" min-width="90px"> </el-table-column>
      <el-table-column label="平仓PNL" prop="clospnl" align="center" min-width="100px"> </el-table-column>
      <el-table-column label="净PNL" prop="netpnl" align="center" min-width="125"> </el-table-column>
       <el-table-column label="风险率" prop="risk" align="center" min-width="120">
          <template slot-scope="{row}">
              <span>{{row.risk | numFilter}}%</span>
          </template>
       </el-table-column>
      <el-table-column label="操作" align="center" v-if="$store.getters.roles.indexOf('setuserlabl')>-1 || $store.getters.roles.indexOf('setuserlabldel')>-1" min-width="180px" class-name="small-padding fixed-width">
        <template slot-scope="{ row, $index }">
          <el-button type="primary" size="mini" @click="setLabel(row, $index)">标签</el-button>
          <el-button type="primary" size="mini" v-if="$store.getters.roles.indexOf('setuserlabldel')>-1" @click="LabelDelClick(row, $index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200,100,50,10]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <!-- 标签 -->
    <el-dialog
      @close="closeSetLabel"
      v-dialogDrag
      :visible.sync="labelDialog"
      width="70%"
      title="设置标签"
    >
      <div class="select_wrap">
        <span>标签</span>
        <el-select
          v-model="labelId"
          class="filter-item"
          placeholder="请选择"
          clearable
          @change="selectLabel"
        >
          <el-option
            v-for="item in labelOptions"
            :key="item.labelid"
            :label="item.label_name"
            :value="item.labelid"
          />
        </el-select>
        <i v-if="$store.getters.roles.indexOf('setuserlabl')>-1" @click="()=>{labelDialog=false;this.$router.push('/riskAdminister/lebelAdminister')}" class="el-icon-circle-plus-outline" style="font-size:24px;padding-left:10px;"></i>
      </div>
      <div v-if="labelId">
        <el-table
            v-loading="labelListLoading"
            :data="lebelList"
            border
            fit
            highlight-current-row
            size="mini"
            style="width: 100%; margin-top: 30px"
            :header-cell-style="{ background: '#F0F8FF' }"
          >
            <el-table-column label="合约" prop="contract_code" align="center" min-width="90"> </el-table-column>
            <el-table-column label="最大杠杆" prop="max_lever" align="center" min-width="110px"> </el-table-column>
           
            <el-table-column label="单笔最大下单量" prop="max_order_volume" align="center" min-width="130px">
                <template slot-scope="{ row }">
                <span>{{ row.max_order_volume || '--' }}</span>
              </template>
            </el-table-column>
              <el-table-column label="单笔最小下单量" prop="min_order_volume" align="center" min-width="130px">
                <template slot-scope="{ row }">
                <span>{{ row.min_order_volume || '--' }}</span>
              </template>
            </el-table-column>
             <el-table-column label="最大持仓量" prop="max_posi_volume" align="center" min-width="110px">
                <template slot-scope="{ row }">
                <span>{{ row.max_posi_volume || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="最小点差" align="center" min-width="110px">
              <template slot-scope="{ row }">
               <span>{{ row.min_slippage}}</span>
              </template>
            </el-table-column>
            <el-table-column label="最大点差" align="center" min-width="110px">
              <template slot-scope="{ row }">
               <span>{{ row.slippage}}</span>
              </template>
            </el-table-column>
            <el-table-column label="手续费率" prop="fee" align="center" min-width="95px">
                <template slot-scope="{ row }">
                <span>{{ row.fee}}</span>
              </template>
            </el-table-column>
            <el-table-column label="资金费率" align="center" min-width="100px">
              <template slot-scope="{ row }">
               <span>{{ row.funding}}</span>
              </template>
            </el-table-column>
            <el-table-column label="风险率增减" prop="risk_rate" align="center" min-width="110px"> </el-table-column>
            <el-table-column label="状态" prop="totalincash" min-width="90px" align="center">
              <template slot-scope="{row}">
                <span>{{row.status_stype == 1 ? '开启':'关闭'}}</span>
              </template>
            </el-table-column>
          </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="!this.labelId" type="primary" @click="handelSetLabel">确定</el-button>
        <!-- 去谷歌验证码 -->
        <!-- <el-button type="primary" @click="()=>{this.checkvkeyDialog = true}">确定</el-button> -->
      </span>
    </el-dialog>
    <el-dialog
      @close="closexgmm"
      class
      title="输入谷歌二维码"
      width="350px"
      :visible.sync="checkvkeyDialog"
      :close-on-click-modal="false"
      @submit.native.prevent
    >
      <el-form 
        :rules="rules" 
        label-position="left"
        ref="ruleForm" 
        :model="ruleForm"
        label-width="auto"
      >
        <el-form-item label="验证码" prop="yzmVal">
          <el-input v-model="ruleForm.yzmVal" class="input_div" autocomplete="off" maxlength="6" ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="checkvkeyDialog = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="entrySendyzm()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 封装api
import { getaccinfo, setuserlabl, setuserlabldel, getaccinfoexport } from "@/api/fundQuery";
import { getlabel, getlabllistbyid, commckeckvkey } from '@/api/user'

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "tradeAssetQuery",
  data() {
    return {
      listLoading: false,
      total: 0,
      assetList: null,
      filters: false, // 控制高级筛选
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        incashmin: undefined, //最小入金
        incashmax: undefined, //最大入金
        closmin: undefined, //最小平仓数
        closmax: undefined, //最大平仓数
        marmin: undefined, //最小盈亏
        marmax: undefined, //最大盈亏
        commismin: undefined, //最小手续费
        commismax: undefined, //最大手续费
        rightsmin: undefined, //最小权益
        rightsmax: undefined, //最大权益
        pageNo: 1,
        pagesize: 10,
      },
      labelDialog: false, // 控制标签弹框显示
      currentItem:{},
      labelOptions:[],
      checkvkeyDialog: false, // 控制google验证弹框显示
      ruleForm: {
        yzmVal: "" // 谷歌验证码
      },
      labelId: undefined,
      labelListLoading: false, 
      rules: {  // 验证规则
        yzmVal: [{ required: true, message: "该输入框不能为空", trigger: "blur" },],
      },
      lebelList:null,
      flag:false,
      exportLoading: false, //导出加载中效果
    };
  },
  components: {},

  computed: {},

  mounted() {
    getlabel({}).then((res)=>{
      this.labelOptions = res.data
    })
    this.getList();
  },
  
filters: {
  numFilter (value){
    let realVal = ''
    if (!isNaN(value) && value!== '') {
      // 截取当前数据到小数点后两位
      realVal = parseFloat(value).toFixed(2)
    } else {
      realVal = '--'
    }
    return realVal
  }
},
  methods: {
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      data.incashmin = data.incashmin || data.incashmin === "0" ? Number(data.incashmin) : undefined;
      data.incashmax = data.incashmax || data.incashmax === "0" ? Number(data.incashmax) : undefined;
      data.closmin = data.closmin || data.closmin === "0" ? Number(data.closmin) : undefined;
      data.closmax = data.closmax || data.closmax === "0" ? Number(data.closmax) : undefined;
      data.marmin = data.marmin || data.marmin === "0" ? Number(data.marmin) : undefined;
      data.marmax = data.marmax || data.marmax === "0" ? Number(data.marmax) : undefined;
      data.commismin = data.commismin || data.commismin === "0" ? Number(data.commismin) : undefined;
      data.commismax = data.commismax || data.commismax === "0" ? Number(data.commismax) : undefined;
      data.rightsmin = data.rightsmin || data.rightsmin === "0" ? Number(data.rightsmin) : undefined;
      data.rightsmax = data.rightsmax || data.rightsmax === "0" ? Number(data.rightsmax) : undefined;
      getaccinfo(data).then((res) => {
        this.assetList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    //高级筛选
    highFilterCheckout(){
      this.filters = !this.filters;
      this.listQuery.incashmin = undefined;
      this.listQuery.incashmax = undefined;
      this.listQuery.closmin = undefined;
      this.listQuery.closmax = undefined;
      this.listQuery.marmin = undefined;
      this.listQuery.marmax = undefined;
      this.listQuery.commismin = undefined;
      this.listQuery.commismax = undefined;
      this.listQuery.rightsmin = undefined;
      this.listQuery.rightsmax = undefined;
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    setLabel(row,index){
      this.labelDialog = true
      this.currentItem = row
    },
    // 选择标签
    selectLabel(val){
      this.labelId = val
      this.flag = true
      this.labelListLoading = true
      if(val){
        getlabllistbyid(val).then((res)=>{
          // console.log(res)
          this.labelListLoading = false
          this.lebelList = res.data
        })
      }
    },
    LabelDelClick(row){
      // console.log(row.userid)
      this.$confirm('确认删除个标签吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // console.log('1111111111')
        setuserlabldel({
          user_id:JSON.parse(row.userid),
        }).then(() => {
          this.$notify({
            title: "删除成功",
            type: "success",
            duration: 2000,
          });
          this.getList();
        });
      })
    },
    closexgmm(){
      this.$refs["ruleForm"].resetFields();
    },
    // 关闭标签事件
    closeSetLabel(){
        this.labelId = undefined
        this.lebelList = null
        this.currentItem = {}
    },
    // 设置标签确认点击事件
    handelSetLabel(){
      setuserlabl({
        user_id: JSON.parse(this.currentItem.userid),
        lablid: Number(this.labelId)
      }).then((res) => {
        this.labelDialog = false;
        this.labelId = undefined
        this.currentItem = {}
        this.$notify({
          title: "成功",
          message: "操作成功",
          type: "success",
          duration: 2000,
        });
        this.getList();
      });
    },
    // 谷歌验证码弹框 确认 点击事件
    entrySendyzm() {
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          // console.log(this.currentItem.userid)
          commckeckvkey({code: this.ruleForm.yzmVal}).then(res=>{
            setuserlabl({
              user_id:JSON.parse(this.currentItem.userid),
              lablid: Number(this.labelId)
            }).then((res) => {
              this.labelDialog = false;
              this.checkvkeyDialog = false;
              this.labelId = undefined
              this.currentItem = []
              this.ruleForm.yzmVal = ''
              this.$notify({
                title: "成功",
                message: "操作成功",
                type: "success",
                duration: 2000,
              });
              this.getList();
            });
          })
        } else {
            return false;
        }
      });
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      getaccinfoexport(data).then((res) => {
        if(res.ret == 0){
          this.$notify.success({title:'操作成功',message:"请到‘交易查询/导出下载列表’进行下载"})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },


  },
};
</script>
<style lang="scss" scoped>
.asset-container{
  .filter-container{
    .highSwitch_wrap{
      margin-top: 15px; 
      width: 100px; 
      cursor: pointer;
      font-size: 14px;
    }
  }
  .high_filter_wrap {
    width: 100%;
    &::v-deep .el-col{
      display: flex;
      flex-wrap: wrap;
    }
    .filter_item_wrap{
      white-space: nowrap;
      margin: 10px 15px 0 0 ;
    }
    .high_filter_key {
      font-size: 14px;
      margin-right: 10px;
    }
    .high_filter_btn_wrap{
      margin: 10px 0 0 0 ;
    }
  }
  .select_wrap{
    // padding: 0 30px 0 30px;
    display: flex;
    align-items: center;
    span{
      width:100px;
      // padding-right: 20px;
    }
  }
}
</style>