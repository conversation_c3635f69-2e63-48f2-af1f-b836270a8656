<template>
  <div class="shareHandlingFee-container">
    <div class="filter-container">
      <span style="font-size: 12px">活动日期</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        class="picker"
        v-model="dataRegisterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change="dataRegisterTimeChange"
      >
      </el-date-picker>
      <el-button
        style="margin-left: 20px"
        v-waves
        class="filter-item"
        size="mini"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        size="mini"
        v-if="$store.getters.roles.indexOf('upuserward') > -1"
        type="success"
        style=""
        @click="setRanking()"
        >修改排行榜</el-button
      >
      <el-button
        size="mini"
        v-if="$store.getters.roles.indexOf('setactivity') > -1"
        type="success"
        style=""
        @click="activitySetClick()"
        >活动设置</el-button
      >
    </div>

    <el-table
      v-loading="listLoading"
      :data="lebelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column
        label="活动日期"
        prop="own_day"
        align="center"
        min-width="100px"
      />
      <el-table-column
        label="活动名称"
        prop="activity_name"
        align="center"
        min-width="95px"
      />
      <el-table-column label="活动描述" align="center" min-width="90px"
        ><template slot-scope="{ row }">
          <span>{{ row.describe || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="奖池最高上限"
        prop="limit_up"
        align="center"
        min-width="120"
      />
      <el-table-column
        label="注入手续费比例"
        prop="ratio"
        align="center"
        min-width="120"
      />
      <el-table-column
        label="开始日期"
        prop="start_day"
        align="center"
        min-width="100px"
      />
      <el-table-column
        label="结束日期"
        prop="end_day"
        min-width="100px"
        align="center"
      />
      <el-table-column
        label="每期开始时间"
        prop="start_time"
        min-width="100px"
        align="center"
      >
        <template slot-scope="{ row }">
          <span
            v-html="
              row.start_time
                ? row.start_time.split(' ')[0] +
                  '<br/>' +
                  row.start_time.split(' ')[1]
                : '--'
            "
          ></span>
        </template>
      </el-table-column>
      <el-table-column
        label="每期开奖时间"
        prop="opening_time"
        align="center"
        min-width="100px"
      >
        <template slot-scope="{ row }">
          <span
            v-html="
              row.opening_time
                ? row.opening_time.split(' ')[0] +
                  '<br/>' +
                  row.opening_time.split(' ')[1]
                : '--'
            "
          ></span>
        </template>
      </el-table-column>
      <el-table-column
        label="每期结束时间"
        prop="end_time"
        align="center"
        min-width="100px"
      >
        <template slot-scope="{ row }">
          <span
            v-html="
              row.end_time
                ? row.end_time.split(' ')[0] +
                  '<br/>' +
                  row.end_time.split(' ')[1]
                : '--'
            "
          ></span>
        </template>
      </el-table-column>
      <el-table-column label="活动状态" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.ac_status ? "进行中" : "已结束" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="已发奖励"
        prop="lssued"
        align="center"
        min-width="120"
      />
      <el-table-column
        label="操作"
        min-width="250"
        align="center"
        v-if="
          $store.getters.roles.indexOf('getuseractivitylist') > -1 ||
          $store.getters.roles.indexOf('setactivityhash') > -1  ||
          $store.getters.roles.indexOf('setactivitylssued') > -1
        "
      >
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            type="primary"
            @click="handleLook(row)"
            v-if="$store.getters.roles.indexOf('getuseractivitylist') > -1"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="primary"
            @click="handleHASH(row)"
            v-if="$store.getters.roles.indexOf('setactivityhash') > -1"
            >HASH</el-button
          >
          <el-button
            size="mini"
            type="primary"
            @click="handleBonus(row)"
            v-if="$store.getters.roles.indexOf('setactivitylssued') > -1"
            >设置金额</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[100,200,300,500]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <!-- 设置hash -->
    <el-dialog
      @close="
        () => {
          this.$refs['setHashForm'].resetFields();
        }
      "
      class
      title="设置HASH"
      width="50%"
      :visible.sync="hashDialog"
      :close-on-click-modal="false"
    >
      <el-form
        :rules="rules"
        label-position="left"
        ref="setHashForm"
        :model="setHashForm"
        label-width="auto"
      >
        <el-form-item label="交易HASH" prop="hashVal">
          <el-input v-model="setHashForm.hashVal"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="hashDialog = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="entrySetHash()"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 设置金额 -->
    <el-dialog
      @close="
        () => {
          this.$refs['setBonusForm'].resetFields();
        }
      "
      class
      title="设置奖池金额"
      width="50%"
      :visible.sync="bonusDialog"
      :close-on-click-modal="false"
    >
      <el-form
        :rules="rules"
        label-position="left"
        ref="setBonusForm"
        :model="setBonusForm"
        label-width="auto"
      >
        <el-form-item label="奖池金额" prop="bonusVal">
          <el-input
            v-model="setBonusForm.bonusVal"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="bonusDialog = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="entrySetBonus()"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 活动设置 -->
    <el-dialog
      @close="closeActivitySetDialog"
      title="活动设置"
      :visible.sync="activitySetDialog"
      width="50%"
      v-dialogDrag
    >
      <el-form
        ref="activitySetForm"
        :rules="rules"
        :model="activitySetForm"
        label-width="auto"
        label-position="left"
      >
        <el-form-item label="活动名称" prop="activity_name">
          <el-input v-model="activitySetForm.activity_name"></el-input>
        </el-form-item>
        <el-form-item label="活动描述" prop="describe">
          <el-input v-model="activitySetForm.describe"></el-input>
        </el-form-item>
        <el-form-item label="奖池最高上限" prop="limit_up">
          <el-input v-model="activitySetForm.limit_up"></el-input>
        </el-form-item>
        <el-form-item label="注入手续费比例" prop="ratio">
          <el-input
            v-model="activitySetForm.ratio"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
          >
            <i slot="suffix" style="font-style: normal">%</i>
          </el-input>
        </el-form-item>
        <el-form-item label="活动日期" prop="start_day">
          <el-date-picker
            style="width: 100%"
            class="picker"
            v-model="start_end_time"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="filterTimeTransform"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="结束日期" prop="end_day">
          <el-date-picker
            v-model="activitySetForm.end_day"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          >
          </el-date-picker>
        </el-form-item> -->
        <el-form-item label="每期开始时间" prop="start_time">
          <el-time-picker
            v-model="activitySetForm.start_time"
            placeholder="选择时间"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            arrow-control
            :picker-options="{
              selectableRange: `00:00:00 - ${activitySetForm.end_time}`,
            }"
          >
          </el-time-picker>
          <el-tooltip
            class="item"
            effect="dark"
            content="开始时间须 小于 结束时间"
            placement="top"
          >
            <i class="el-icon-warning"></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="每期开奖时间" prop="opening_time">
          <el-time-picker
            v-model="activitySetForm.opening_time"
            placeholder="选择时间"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            arrow-control
            :picker-options="{
              selectableRange: `${activitySetForm.start_time} - ${activitySetForm.end_time}`,
            }"
          >
          </el-time-picker>
          <el-tooltip
            class="item"
            effect="dark"
            content="开奖时间须在 开始-结束 时间区间内"
            placement="top"
          >
            <i class="el-icon-warning"></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="每期结束时间" prop="end_time">
          <el-time-picker
            v-model="activitySetForm.end_time"
            placeholder="选择时间"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            arrow-control
            :picker-options="{
              selectableRange: `${activitySetForm.start_time} - 23:59:59`,
            }"
          >
          </el-time-picker>
          <el-tooltip
            class="item"
            effect="dark"
            content="结束时间须 大于 开始时间"
            placement="top"
          >
            <i class="el-icon-warning"></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="一等奖占比" prop="first_prize">
          <el-input
            v-model="activitySetForm.first_prize"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
          >
            <i slot="suffix" style="font-style: normal">%</i>
          </el-input>
        </el-form-item>
        <el-form-item label="二等奖占比" prop="second_award">
          <el-input
            v-model="activitySetForm.second_award"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
          >
            <i slot="suffix" style="font-style: normal">%</i>
          </el-input>
        </el-form-item>
        <el-form-item label="三等奖占比" prop="third_award">
          <el-input
            v-model="activitySetForm.third_award"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
          >
            <i slot="suffix" style="font-style: normal">%</i>
          </el-input>
        </el-form-item>
        <el-form-item label="活动状态" prop="status">
          <el-select v-model="activitySetForm.status" placeholder="请选择">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="activitySetDialogEntry()"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <!-- 排行榜 -->
    <el-dialog
      @close="
        () => {
          this.$refs['setRankingForm'].resetFields();
        }
      "
      class
      title="修改排行榜"
      width="750px"
      :visible.sync="setRankingDialog"
      :close-on-click-modal="false"
    >
      <el-table
        v-loading="rankingListLoading"
        :data="rankingList"
        border
        fit
        highlight-current-row
      size="mini"
        style="width: 100%; margin-top: -20px"
        :header-cell-style="{ background: '#F0F8FF' }"
        class="setRankingWrap"
      >
        <el-table-column type="index" align="center" label="名次" width="50">
        </el-table-column>
        <el-table-column label="UID" align="center" min-width="78"
          ><template slot-scope="{ row }">
            <span>{{ row.user_id || "--" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="累计获奖金额" min-width="80px" align="center">
          <template slot-scope="{ row }">
            <span v-if="row.is_real">{{ row.totalward }}</span>
            <el-input
              class="totalwardInput"
              v-else
              v-model="row.totalward"
              @blur="updateRanking(row)"
            ></el-input>
          </template>
        </el-table-column>
      </el-table>
      <el-form
        :rules="rules"
        ref="setRankingForm"
        :model="setRankingForm"
        :inline="true"
        style="margin: 0; margin-top: 20px"
      >
        <el-form-item label="UID" prop="user_id">
          <el-input size="mini" v-model="setRankingForm.user_id"></el-input>
        </el-form-item>
        <el-form-item label="累计获奖金额" prop="totalward">
          <el-input
            size="mini"
            v-model="setRankingForm.totalward"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="setRankingFormSubmit"
            >确定插入</el-button
          >
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="setRankingDialog = false"
          >关 闭</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      @close="closexgmm"
      class
      title="输入谷歌二维码"
      width="350px"
      :visible.sync="checkvkeyDialog"
      :close-on-click-modal="false"
      @submit.native.prevent
    >
      <el-form
        :rules="rules"
        label-position="left"
        ref="ruleForm"
        :model="ruleForm"
        label-width="auto"
      >
        <el-form-item label="验证码" prop="yzmVal">
          <el-input v-model="ruleForm.yzmVal" maxlength="6"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="checkvkeyDialog = false"
          >取 消</el-button
        >
        <el-button size="mini" type="primary" @click="entrySendyzm()"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  bcactivitylist,
  setactivityhash,
  setactivitylssued,
  getuserwardlist,
  inuserwardlist,
  upuserward,
  setactivity,
  getnowactivity,
} from "@/api/activity";
import { bcprocontractset, getlabel, commckeckvkey } from "@/api/user";
//引入的自定义指令
import waves from "@/directive/waves";

export default {
  name: "shareHandlingFee",
  directives: { waves },
  data() {
    var awardRules = (rules, value, callback) => {
      if (!value) {
        return callback(new Error("该输入框不能为空"));
      } else if (
          Number(this.activitySetForm.first_prize) +
          Number(this.activitySetForm.second_award) +
          Number(this.activitySetForm.third_award) >
        100
      ) {
        return callback(new Error("奖励总占比不能大于100%"));
      } else {
        callback();
      }
    };
    var start_end_timeRules = (rules, value, callback) => {
      console.log(value);
      if (!value) {
        return callback(new Error("该输入框不能为空"));
      } else if (
        Number(this.activitySetForm.first_prize) +
          Number(this.activitySetForm.second_award) +
          Number(this.activitySetForm.third_award) >
        100
      ) {
        return callback(new Error("奖励总占比不能大于100%"));
      } else {
        callback();
      }
    };
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 86400000;
        },
      },
      listLoading: false,
      total: 0,
      lebelList: null,
      dataRegisterTime: [], // 活动日期 筛选
      listQuery: {
        pageNo: 1,
        pagesize: 100,
        start: "",
        end: "",
      },
      hashDialog: false, // 修改hash控制弹框
      setHashForm: {
        hashVal: "", // 修改hash 绑定值
      },
      bonusDialog: false, // 修改金额控制弹框
      setBonusForm: {
        bonusVal: "", // 修改金额 绑定值
      },
      // 活动设置
      activitySetDialog: false, //控制活动设置弹框显示隐藏
      activitySetForm: {
        limit_up: "", //  奖金限制，
        ratio: "", //   手续费比例，
        start_time: "", //  开始时间，
        end_time: "", //  结束时间，
        opening_time: "", //  开奖时间，
        first_prize: "", //   一等奖比例，
        third_award: "", //   三等奖比例，
        second_award: "", //  二等奖比例，
        activity_name: "", //   活动名字，
        start_day: "", //   开始日期，
        end_day: "", //   结束日期，
        describe: "", //  描述，
        status: 1, //  状态 1 开启 0 关闭
      },
      start_end_time: [],
      // google
      checkvkeyDialog: false, // 控制google验证弹框显示
      ruleForm: {
        yzmVal: "", // 谷歌验证码
      },
      // 排行榜
      setRankingDialog: false, //控制活动设置弹框显示隐藏
      rankingListLoading: false,
      rankingList: [], // 排行列表
      setRankingForm: {
        user_id: "", //  uid
        totalward: "", //   累计金额
      },

      rules: {
        yzmVal: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        hashVal: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        bonusVal: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        activity_name: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        describe: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        limit_up: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        ratio: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        start_day: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        end_day: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        // start_end_time: [
        //   {
        //     type: "array",
        //     required: true,
        //     trigger: "change",
        //     validator: (rule, value, cb) => {
        //       console.log(rule, value, );
        //       // cb(new Error(`value=${value}, dtr=${this.form.valueRange}`));
        //     },
        //   },
        // ],

        start_time: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        opening_time: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        end_time: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        first_prize: [
          { required: true, validator: awardRules, trigger: "blur" },
        ],
        second_award: [
          { required: true, validator: awardRules, trigger: "blur" },
        ],
        third_award: [
          { required: true, validator: awardRules, trigger: "blur" },
        ],
        status: [{ required: true, message: "请选择", trigger: "change" }],
        user_id: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        totalward: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
      },
      statusOptions: [
        {
          value: 1,
          label: "开启",
        },
        {
          value: 0,
          label: "关闭",
        },
      ],
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    // 赋值活动设置 日期
    filterTimeTransform(val) {
      this.activitySetForm.start_day = (val && val[0]) || "";
      this.activitySetForm.end_day = (val && val[1]) || "";
    },
    // 修改排行
    updateRanking(row) {
      console.log(row);
      if (row.copyTotalward == row.totalward) return;
      upuserward({
        user_id: row.user_id,
        totalward: Number(row.totalward),
      }).then((res) => {
        this.rankingListLoading = true;
        getuserwardlist().then((response) => {
          this.rankingList = response.data;
          this.rankingListLoading = false;
        });
        this.$notify({
          title: "成功",
          message: "操作成功",
          type: "success",
          duration: 2000,
        });
      });
    },
    // 确定插入 点击事件
    setRankingFormSubmit() {
      this.$refs["setRankingForm"].validate((valid) => {
        if (valid) {
          inuserwardlist({
            user_id: this.setRankingForm.user_id,
            totalward: Number(this.setRankingForm.totalward),
          }).then((res) => {
            this.rankingListLoading = true;
            getuserwardlist().then((response) => {
              this.rankingList = response.data;
              this.rankingListLoading = false;
            });
            this.$notify({
              title: "成功",
              message: "操作成功",
              type: "success",
              duration: 2000,
            });
          });
        } else {
          return false;
        }
      });
    },
    // 修改排行榜 入口
    setRanking() {
      this.setRankingDialog = true;
      this.rankingListLoading = true;
      getuserwardlist().then((response) => {
        this.rankingList = response.data.map((v) => {
          v.copyTotalward = v.totalward;
          return v;
        });
        this.rankingListLoading = false;
      });
    },
    // 活动设置弹框 确定 点击事件
    activitySetDialogEntry() {
      this.$refs["activitySetForm"].validate((valid) => {
        if (valid) {
          this.checkvkeyDialog = true;
        }
      });
    },
    // 关闭活动设置弹框触发事件
    closeActivitySetDialog() {
      this.activitySetForm = {
        label_id: null, //主标签 id
        contract_code: null, //合约代码
        max_lever: null, // 最大杠杆
        max_order_volume: null, // 最大下单
        min_order_volume: null, // 最小下单
        max_posi_volume: null, //  最大持仓
        fee: null, // 手续费加点
        funding: null, // 资金费用加点
        slippage: null, //滑点
        risk_rate: null,
      };
      this.$refs["activitySetForm"].resetFields();
    },
    // 设置奖池金额弹框确认按钮点击事件
    entrySetBonus() {
      this.$refs["setBonusForm"].validate((valid) => {
        if (valid) {
          setactivitylssued({
            id: this.setBonusForm.id,
            bonus: Number(this.setBonusForm.bonusVal),
          }).then((res) => {
            this.bonusDialog = false;
            this.getList();
            this.$notify({
              title: "成功",
              message: "操作成功",
              type: "success",
              duration: 2000,
            });
          });
        } else {
          return false;
        }
      });
    },
    // 设置金额入口按钮
    handleBonus(row) {
      this.setBonusForm.bonusVal = row.set_bonus;
      this.setBonusForm.id = row.id;
      this.bonusDialog = true;
    },
    // 设置hash弹框确认按钮点击事件
    entrySetHash() {
      this.$refs["setHashForm"].validate((valid) => {
        if (valid) {
          setactivityhash({
            id: this.setHashForm.id,
            hash: this.setHashForm.hashVal,
          }).then((res) => {
            this.hashDialog = false;
            this.getList();
            this.$notify({
              title: "成功",
              message: "操作成功",
              type: "success",
              duration: 2000,
            });
          });
        } else {
          return false;
        }
      });
    },
    // 设置hash入口按钮
    handleHASH(row) {
      this.setHashForm.hashVal = row.hash;
      this.setHashForm.id = row.id;
      this.hashDialog = true;
    },
    // 查看
    handleLook(row) {
      this.$router.push({
        path: "/activity/feeDetails",
        query: {
          dt: JSON.stringify(row),
        },
      });
    },
    // 回车搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    // 赋值日期
    dataRegisterTimeChange(val) {
      if (val) {
        this.listQuery.start = val[0];
        this.listQuery.end = val[1] + " 23:59:59";
      } else {
        this.listQuery.start = "";
        this.listQuery.end = "";
      }
    },
    //点击添加标签出现弹框
    activitySetClick() {
      getnowactivity().then((res) => {
        this.activitySetForm = res.data;
        this.activitySetForm.start_time =
          res.data.start_time.split(" ")[1] || "00:00:00";
        this.activitySetForm.end_time =
          res.data.end_time.split(" ")[1] || "12:00:00";
        this.activitySetForm.opening_time =
          res.data.opening_time.split(" ")[1] || "23:59:59";
        this.start_end_time = [res.data.start_day, res.data.end_day];
        this.activitySetDialog = true;

        this.$nextTick(() => {
          this.$refs["activitySetForm"].clearValidate();
        });
      });
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      bcactivitylist(this.listQuery).then((response) => {
        this.lebelList = response.data.list;
        // .map((v) => {
        //   // let start = new Date(v.start_time).getTime();
        //   // let open = new Date(v.opening_time).getTime();
        //   // let end = new Date(v.end_time).getTime();
        //   // if (open < start) {
        //   //   v.status = "";
        //   // }
        //   return v;
        // });
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    // google验证码弹框关闭触发事件
    closexgmm() {
      this.$refs["ruleForm"].resetFields();
    },
    entrySendyzm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          commckeckvkey({ code: this.ruleForm.yzmVal }).then((res) => {
            let data = this.activitySetForm;
            Object.assign(data, {
              start_time: data.start_day + " " + data.start_time,
              opening_time: data.start_day + " " + data.opening_time,
              end_time: data.start_day + " " + data.end_time,
              ratio: Number(data.ratio),
              limit_up: Number(data.limit_up),
              first_prize: Number(data.first_prize),
              second_award: Number(data.second_award),
              third_award: Number(data.third_award),
            });
            // for (const key in data) {
            //   if (data.hasOwnProperty(key)) {
            //     const element = data[key];
            //     if (/^[0-9]+.?[0-9]*$/.test(element)) {
            //       data[key] = Number(element) || 0;
            //     } else {
            //       if (!element) {
            //         data[key] = 0;
            //       }
            //     }
            //   }
            // }
            setactivity(data).then((res) => {
              this.ruleForm.yzmVal = "";
              this.closeActivitySetDialog();
              this.checkvkeyDialog = false;
              this.activitySetDialog = false;
              this.$notify({
                title: "设置",
                message: "操作成功",
                type: "success",
                duration: 2000,
              });
              this.getList();
            });
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.shareHandlingFee-container {
  .totalwardInput {
    .el-input__inner {
      text-align: center;
    }
  }
}
</style>
<style lang="scss" scoped>
</style>