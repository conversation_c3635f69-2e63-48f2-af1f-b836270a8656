import { login, logout, getInfo, checkVkey } from '@/api/user'
import { getToken, setToken, removeToken, getRoles, setRoles, removeRoles } from '@/utils/auth'
import { resetRouter } from '@/router'
import Cookies from 'js-cookie'

const getDefaultState = () => {
    return {
        token: getToken(),
        name: '',
        avatar: '',
        roles: [],
       
    }
}

const state = getDefaultState()

const mutations = {
   
    RESET_STATE: (state) => {
        Object.assign(state, getDefaultState())
    },
    SET_TOKEN: (state, token) => {
        state.token = token
    },
    SET_NAME: (state, name) => {
        state.name = name
    },
    SET_AVATAR: (state, avatar) => {
        state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
        state.roles = roles
    }
}

const actions = {
    // user login
    checkVkey({ state }, data) {
        return new Promise((resolve, reject) => {
            checkVkey(data).then(response => {
                setToken(state.token)
                let roles = []
                let obj = JSON.parse(JSON.parse(response.data.ModelRols))
                // let obj = JSON.parse(response.data.ModelRols)
                // console.log(obj)
                for (const key in obj) {
                    if (obj[key]) {
                        roles.push(key)
                    }
                }
                setRoles(roles)
                // console.log(roles)
                resolve()
            }).catch(error => {
                reject(error)
            })
        })
    },


    // get user info
    getInfo({ commit, state }) {
        return new Promise((resolve, reject) => {
            getInfo(state.token).then(response => {
                const { data } = response
                console.log(data)
                if (!data) {
                    reject('Verification failed, please Login again.')
                }

                const { roles, name, avatar } = data

                // roles must be a non-empty array
                if (!roles || roles.length <= 0) {
                    reject('getInfo: roles must be a non-null array!')
                }
                commit('SET_ROLES', roles)
                commit('SET_NAME', name)
                commit('SET_AVATAR', avatar)
                resolve(data)
            }).catch(error => {
                reject(error)
            })
        })
    },

    // user logout
    logout({ commit, state }) {
        return new Promise((resolve, reject) => {
            removeToken() // must remove  token  first
            removeRoles() // must remove  token  first
            resetRouter()
            Cookies.remove('username');
            commit('RESET_STATE')
            resolve()
        })
    },

    // remove token
    resetToken({ commit }) {
        return new Promise(resolve => {
            removeToken() // must remove  token  first
            commit('RESET_STATE')
            resolve()
        })
    }
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}