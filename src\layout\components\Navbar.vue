<template>
  <div class="navbar">
    <hamburger
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <breadcrumb class="breadcrumb-container" />

    <div class="right-menu">
      <el-dropdown class="avatar-container" trigger="click">
        <span>{{username}}</span>
        <i class="el-icon-caret-bottom" />

        <el-dropdown-menu
          slot="dropdown"
          style="width: 240px; position: absolute; left: 1656px"
        >
          <router-link to="/">
            <el-dropdown-item> 首页 </el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="modifyPass">
            <span style="display: block">修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span style="display: block">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-dialog
      title=" "
      :visible.sync="modifyPassDialog"
      @close="closexgmm"
      :close-on-click-modal="false"
      width="50%"
    >
      <el-form  :rules="rules" ref="ruleForm" :model="ruleForm">
        <el-form-item label="输入原密码" prop="oldPass" label-width="83px">
          <el-input
            type="password"
            placeholder="请填写原密码"
            v-model="ruleForm.oldPass"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="输入新密码" prop="newPass" label-width="83px">
          <el-input
            type="password"
            placeholder="请填写6~16位数新密码"
            v-model="ruleForm.newPass"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="确认新密码" prop="checkPass" label-width="83px">
          <el-input
            type="password"
            placeholder="请再次确认新密码"
            v-model="ruleForm.checkPass"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closexgmm()">取 消</el-button>
        <el-button size="mini" type="primary" @click="entry()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import { upmanagepass } from "@/api/user";
import Cookies from 'js-cookie'
export default {
  data(){
    var checkAge = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("原密码不能为空"));
      } else {
        callback();
      }
    };
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else if (!/^[0-9a-zA-Z]+$/.test(value)) {
        callback(new Error("只能输入英文和数字"));
      } else if (!/^\w{4,16}$/.test(value)) {
        callback(new Error("长度必须在6~16位"));
      } else {
        if (this.ruleForm.checkPass !== "") {
          this.$refs.ruleForm.validateField("checkPass");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (!/^[0-9a-zA-Z]+$/.test(value)) {
        callback(new Error("只能输入英文和数字"));
      } else if (value !== this.ruleForm.newPass) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      modifyPassDialog: false,
      ruleForm: {
        oldPass: "",
        newPass: "",
        checkPass: ""
      },
      rules: {
        newPass: [{ validator: validatePass, trigger: "blur" }],
        checkPass: [{ validator: validatePass2, trigger: "blur" }],
        oldPass: [{ validator: checkAge, trigger: "blur" }]
      },
      username:''
    }
  },
  components: {
    Breadcrumb,
    Hamburger,
  },
  computed: {
    ...mapGetters([
      "sidebar",
      "avatar",
      //从vuex传过来用户名
      "name",
    ]),
  },
  created() {
    // console.log(this.$store.state.name)
    this.username = Cookies.get('username')
  },
  methods: {
    copy() {
      document.execCommand("Copy"); // 执行浏览器复制命令
      this.$message({
        message: "复制成功",
        type: "success",
      });
    },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    closexgmm() {
      this.$refs["ruleForm"].resetFields();
      this.modifyPassDialog = false;
      this.ruleForm.oldPass = "";
      this.ruleForm.newPass = "";
      this.ruleForm.checkPass = "";
    },
    entry() {
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          upmanagepass({
            pwd: this.ruleForm.oldPass,
            pwd1: this.ruleForm.newPass,
            pwd2: this.ruleForm.checkPass,
          }).then((res)=>{
            // console.log(res)
            this.$notify({
              title: '修改',
              message: "修改成功",
              type: "success",
              duration: 2000,
            })
            setTimeout(()=>{
              this.closexgmm()
              this.logout()
            },500)
          })
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    modifyPass(){
      this.modifyPassDialog = true
    },
    async logout() {
      await this.$store.dispatch("user/logout");
      this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    // line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;
      cursor: pointer;
      margin-top: 20px;
      .avatar-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 40px;
        margin-top: 10px;
        // position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }
        .el-dropdown-menu {
          width: 200px;
          height: 500px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
