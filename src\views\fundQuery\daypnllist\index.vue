<template>
  <div class="daypnllist-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 160px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="font-size: 12px; margin-left: 20px">时间</span>
      <el-date-picker
        style="margin-left:5px;width: 220px; margin-top: 10px"
        v-model="filterTime"
        :clearable="false"
        size="mini" 
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change='filterTimeTransform'
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px;"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>


    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="合约名称" prop="contract_code" min-width="90px" align="center"></el-table-column>
      <el-table-column align="center" label="平多">
        <el-table-column label="平多数量" prop="close_buy_amount" min-width="90px" align="center"></el-table-column>
        <el-table-column label="平多PNL" prop="close_buy_pnl" min-width="90px" align="center"></el-table-column>
        <el-table-column label="多仓手续费" prop="buy_commission" min-width="90px" align="center"></el-table-column>
      </el-table-column>
      <el-table-column align="center" label="平空">
        <el-table-column label="平空数量" prop="close_sell_amount" min-width="90px" align="center"></el-table-column>
        <el-table-column label="平空PNL" prop="close_sell_pnl" min-width="90px" align="center"></el-table-column>
        <el-table-column label="空仓手续费" prop="sell_commission" min-width="90px" align="center"></el-table-column>
      </el-table-column>
      <el-table-column label="买卖单差" prop="buy_sub_sell_amount" align="center" min-width="100px"> </el-table-column>
      <el-table-column label="合计PNL" prop="total_pnl" align="center" min-width="100px"> </el-table-column>
      <el-table-column label="合计手续费" prop="total_commission" align="center" min-width="100px"> </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[100,200,300,500]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { getpnlperday } from "@/api/fundQuery";
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "daypnllist",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        sname: "",
        sagent: "",
        pageNo: 1,
        pagesize: 100,
        star: '', //开始
        end: '', //结束
      },
      accountTypeOptions: [
        { key: 1, name: '全仓' },
        { key: 2, name: '逐仓' },
      ],
      sizeOptions: [
        { key: "B", name: '买入开多' },
        { key: "S", name: '卖出开空' },
      ],
      orderTypeObj: {
        0: "市价单",
        1: "计划单", 
        2: "止盈单", 
        4: "止损单", 
        5: "强平单"
      },
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutStartTime]
    }
  },

  mounted() {
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
      this.listQuery.end = this.filterTime ? this.filterTime[1] : "";
      getpnlperday(this.listQuery).then((res) => {
        this.openList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]:'';
    },
  },
};
</script>
<style lang="scss" scoped>
</style>