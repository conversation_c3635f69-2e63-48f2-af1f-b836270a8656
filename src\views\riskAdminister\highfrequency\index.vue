<template>
  <div class="kfc-container">
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="user_id" align="center" min-width="78"> </el-table-column>
      <el-table-column label="用户名" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="交易次数" prop="trader_num" align="center" min-width="125"> </el-table-column>
      <el-table-column label="开仓次数" prop="open_num" align="center" min-width="125"> </el-table-column>
      <el-table-column label="平仓次数" prop="close_num" align="center" min-width="125"> </el-table-column>
      <el-table-column label="单张平均时间" prop="trading_time" align="center" min-width="125"> </el-table-column>
      <el-table-column label="胜率平仓次数" prop="win_trading_close" align="center" min-width="125"> </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200,100,50,20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//引入封装接口
import {
  highfrequency,
} from "@/api/riskAdminister";

const typeOptions = {
  1: '平仓导出',
  2: '开仓导出'
};
const statusOptions = {
  0: '导出中',
  1: '导出成功',
  2: '导出失败'
};
export default {
  name: "highfrequency",
  data() {
    
    return {
      total: 0,
      tableData: [],
      listLoading: true,
      listQuery: {
        pageNo: 1,
        pagesize: 20,
      },
      updata: {
        
      },
      kycstate:{
          //备注
      content:''
      },
      updatas:[],
      ResetDialogVisible: false,//重置弹框显示控制
      typeOptions,//审核状态 Options
      statusOptions,
      stat:[],
      statIndex:[],
      rules:{
        audit_results:[
          { required: true,message:'请选择', trigger: ['blur','change'] },
        ],
        err_info:[
          { required: true,message:'必填项', trigger: ['blur'] },
        ]
      },
    
      //判断审核弹框里面是拒绝还是通过
      kycselet:false
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    // 回车搜索事件
    handleFilter(){
      this.getList();
    },
    //  table列表数据
    getList() {
      //开始有加载中效果
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      highfrequency(data).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
        // console.log(this.stat)
      });
    },
    exportHandle(row){
      window.location.href = row.url;
    },  
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 0 10px;
 
  // justify-content: space-around;
  .wc_1-one {
    width: 45%;
  }
  .idPhotoImg_wrap ::v-deep .el-image__inner{
    width: auto;
   
  }
  .idPhotoImg_wrap ::v-deep .el-image__error{
    min-width: 100px;
  }
}
</style>