<template>
  <div class="frequency-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID/昵称"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 150px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.tradetount"
        size="mini"
        placeholder="交易次数"
        style="width: 150px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.opencount"
        size="mini"
        placeholder="开仓次数"
        style="width: 150px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.closecount"
        size="mini"
        placeholder="平仓次数"
        style="width: 150px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        placeholder="合约"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>

      <span style="margin-left: 20px; font-size: 12px">成交时间</span>
      <el-date-picker
        style="width: 220px; margin-top: 5px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change="filterTimeTransform"
      />
      <!-- <span style="margin-left: 20px; font-size: 12px">开仓张数</span>
      <el-input
        v-model="listQuery.volume_min"
        size="mini"
        style="width: 80px; margin-left: 5px; margin-top: 5px"
        class="filter-item"
        @input="
          () => {
            listQuery.volume_min = listQuery.volume_min.replace(/[^0-9]/g, '');
          }
        "
        @keyup.enter.native="handleFilter"
      >
        <span slot="suffix"> 张 </span>
      </el-input>
      <span style="margin-left: 5px; font-size: 12px">-</span>
      <el-input
        v-model="listQuery.volume_max"
        size="mini"
        style="width: 80px; margin-left: 5px; margin-top: 5px"
        class="filter-item"
        @input="
          () => {
            listQuery.volume_max = listQuery.volume_max.replace(/[^0-9]/g, '');
          }
        "
        @keyup.enter.native="handleFilter"
      >
        <span slot="suffix"> 张 </span>
      </el-input> -->

      <!-- <span style="margin-left: 20px; font-size: 12px"> 交易次数 </span>
      <el-input
        v-model="listQuery.jy"
        style="width: 150px; margin-top: 5px"
        size="mini"
      >
        <i slot="suffix" style="font-style: normal; line-height: 30px">笔</i>
      </el-input> -->

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px; margin-top: 5px"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        v-if="$store.getters.roles.indexOf('tradefrequencyexport')>-1"
        :loading="exportLoading"
        size="mini"
        type="success"
        style="margin-left: 20px; margin-top: 5px"
        @click="handleExport"
      >
        导出
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="userid" align="center" min-width="78">
      </el-table-column>
      <el-table-column
        label="用户名"
        prop="user_name"
        align="center"
        min-width="95"
      >
      </el-table-column>
      <el-table-column
        label="顶级代理ID"
        prop="top_agent_id"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="上级ID"
        prop="pareid"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.pareid || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="合约"
        prop="contractcode"
        min-width="90px"
        align="center"
      ></el-table-column>
      <el-table-column label="交易次数" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.frequency || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开仓次数" align="center" min-width="80px">
        <template slot-scope="{ row }">
          <span>{{ row.opening }}</span>
        </template>
      </el-table-column>
      <el-table-column label="平仓次数" align="center" min-width="80px">
        <template slot-scope="{ row }">
          <span>{{ row.closeing }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开仓张数" align="center" min-width="80px">
        <template slot-scope="{ row }">
          <span>{{ row.volume }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="IP地址"
        prop="ipaddress"
        align="center"
        min-width="120px"
      >
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="210px">
        <template slot-scope="{ row }">
          <!-- <el-dropdown @command="handleQCFY">
            <el-button size="mini" type="success">
              {{row.isignorere?'撤回去除返佣':'去除返佣'}}<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="beforeHandleCommand(row,'yesterday')">去除昨日</el-dropdown-item>
              <el-dropdown-item :command="beforeHandleCommand(row,'today')">去除当日</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
          <el-button
            v-if="$store.getters.roles.indexOf('setignorerebate') > -1"
            type="success"
            size="mini"
            @click="handleQCFY(row)"
            >{{ row.isignorere ? "撤回去除返佣" : "去除返佣" }}</el-button
          >
          <el-button type="primary" size="mini" @click="handleCK(row)"
            >查看</el-button
          >
          <!--  style="margin-left:10px;" -->
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 20, 30, 50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { tradefrequency, setignorerebate, tradefrequencyexport } from "@/api/riskAdminister";
import { bcprocontractset } from "@/api/user";
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "frequency",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        // account_type: undefined, //账户模式 1：全仓 2：逐仓
        tradetount: "", // 交易次数
        opencount: "", // 开仓次数
        closecount: "", // 平仓次数
        contract_code: "", //合约代码
        // side: undefined, //方向 B买S卖
        // volume_min: "", // 开仓张数
        // volume_max: "", // 开仓张数
        pageNo: 1,
        pagesize: 10,
        star: "", //开始
        end: "", //结束
        jg: "",
        jy: "",
      },
      contractOptions: [],
      exportLoading: false, //导出加载中效果
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault() {
      let date = new Date();
      // 通过时间戳计算
      let defalutStartTime = (
        (date.getTime() - 3 * 24 * 3600 * 1000) /
        1000
      ).toDate("yyyy-MM-dd"); // 转化为时间戳
      let defalutEndTime = (date.getTime() / 1000).toDate("yyyy-MM-dd");
      return [defalutStartTime, defalutEndTime];
    },
  },
  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter((v) => v.isshow == 1);
    });
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    //返回新的command对象
    beforeHandleCommand(row, command) {
      //index我这里是遍历的角标，即你需要传递的额外参数
      return Object.assign(row, { dateStr: command });
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {};
      this.listQuery.tradetount =
        Number(this.listQuery.tradetount) || undefined;
      this.listQuery.opencount = Number(this.listQuery.opencount) || undefined;
      this.listQuery.closecount =
        Number(this.listQuery.closecount) || undefined;
      this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
      this.listQuery.end = this.filterTime
        ? this.filterTime[1] + " 23:59:59"
        : "";
      Object.assign(data, this.listQuery, {
        // volume_min: Number(this.listQuery.volume_min) || 0,
        // volume_max: Number(this.listQuery.volume_max) || 0,
      });
      tradefrequencyexport(data).then((res) => {
        if(res.ret == 0){
          this.$notify.success({title:'操作成功',message:"请到‘交易查询/导出下载列表’进行下载"})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    getList() {
      this.listLoading = true;
      let data = {};
      this.listQuery.tradetount =
        Number(this.listQuery.tradetount) || undefined;
      this.listQuery.opencount = Number(this.listQuery.opencount) || undefined;
      this.listQuery.closecount =
        Number(this.listQuery.closecount) || undefined;
      this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
      this.listQuery.end = this.filterTime
        ? this.filterTime[1] + " 23:59:59"
        : "";
      Object.assign(data, this.listQuery, {
        volume_min: Number(this.listQuery.volume_min) || 0,
        volume_max: Number(this.listQuery.volume_max) || 0,
      });
      tradefrequency(data).then((res) => {
        this.openList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    //点击查看进入详情页
    handleCK(row) {
      // console.log(row)
      this.$router.push({
        path: "/riskAdminister/frequencyDetail",
        query: {
          id: JSON.parse(row.userid),
          star: this.listQuery.star,
          end: this.listQuery.end,
          contract_code: this.listQuery.contract_code,
        },
      });
    },
    //去除返佣
    handleQCFY(row) {
      // let date = new Date()
      // let today = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      // let yesterday = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd')
      // ${row.dateStr == 'today'?'当日':'昨日'}
      this.$confirm(
        `是否确认${row.isignorere ? " 撤回 " : ""}去除此用户的返佣？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          setignorerebate({
            uid: row.userid,
            stype: row.isignorere ? 1 : 2,
            // tdate: row.dateStr == 'today'?today:yesterday
          }).then((res) => {
            this.$notify.success({ title: "成功", message: "操作成功" });
            this.getList();
          });
        })
        .catch(() => {});
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = val ? val[1] + " 23:59:59" : "";
    },
  },
};
</script>
<style lang="scss">
.frequency-cantainer {
  .el-input__prefix,
  .el-input__suffix {
    display: flex;
    align-items: center;
  }
  .el-input--suffix .el-input__inner {
    padding-left: 8px;
    padding-right: 20px;
  }
}
</style>