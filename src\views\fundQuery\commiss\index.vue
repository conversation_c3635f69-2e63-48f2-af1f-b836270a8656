<template>
  <div class="commiss-cantainer">
    <div class="filter-container">
      <el-select
        size="mini"
        v-model="listQuery.contcode"
        placeholder="合约"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <span style="margin-left: 20px; font-size: 12px">成交时间</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change='filterTimeTransform'
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px;"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('getcommissexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        导出
      </el-button>
    </div>


    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="合约" prop="contact_code" min-width="90px" align="center"></el-table-column>
      <el-table-column label="Maker手续费率" prop="fee_maker" min-width="90px" align="center"></el-table-column>
      <el-table-column label="Taker手续费率" prop="fee_taker" min-width="90px" align="center"></el-table-column>
      <el-table-column label="总计" prop="total_commis" align="center" min-width="100px"> </el-table-column>
      <el-table-column label="结算时间" prop="creat_time" align="center" min-width="100px"> </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[100,200,300,500]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { getcommiss, getcommissexport } from "@/api/fundQuery";
import { bcprocontractset } from "@/api/user";
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "commiss",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        contcode: "", //合约代码
        pageNo: 1,
        pagesize: 100,
        star: '', //开始
        end: '', //结束
      },
      contractOptions: [],
      accountTypeOptions: [
        { key: 1, name: '全仓' },
        { key: 2, name: '逐仓' },
      ],
      sizeOptions: [
        { key: "B", name: '买入开多' },
        { key: "S", name: '卖出开空' },
      ],
      orderTypeObj: {
        0: "市价单",
        1: "计划单", 
        2: "止盈单", 
        4: "止损单", 
        5: "强平单"
      },
      exportLoading: false, //导出加载中效果
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      this.listQuery.side = this.listQuery.side || undefined
      this.listQuery.account_type = this.listQuery.account_type || undefined
      this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
      this.listQuery.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
      getcommiss(this.listQuery).then((res) => {
        this.openList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      getcommissexport(data).then((res) => {
        if(res.ret == 0){
          this.$notify.success({title:'操作成功',message:"请到‘交易查询/导出下载列表’进行下载"})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
  },
};
</script>
<style lang="scss" scoped>
</style>