<template>
  <div class="hold-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID/昵称"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 150px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        placeholder="合约"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.account_type"
        placeholder="仓位类型"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        placeholder="方向"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>

      <el-button
        class="filter-item"
        size="mini"
        style="margin-left: 20px; margin-top: 5px"
        type="primary"
        @click="handleFilter"
      >
        <!-- @click="handleFilter" -->
        搜索
      </el-button>
      <!-- <el-button
        class="filter-item"
        :loading="downloadLoading"
        @click="handleDownload"
        size="mini"
        type="success"
      >
        导出
      </el-button> -->
    </div>

    <el-table
      v-loading="listLoading"
      :data="holdList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="user_id" align="center" min-width="78"> </el-table-column>
      <el-table-column label="用户名" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="合约" prop="contract_code" min-width="90px" align="center"></el-table-column>
      <el-table-column label="仓位类型" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <!-- 仓位类型是判断trader_uid大于0就是跟单否则就是带单 -->
          <span>{{row.trader_uid>0?'跟单':'带单'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="方向" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?'卖出开空':'买入开多'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="杠杆" align="center" min-width="60px">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="volume" align="center" min-width="110px">
          <template slot-scope="{ row }">
          <span>{{ row.volume }}张</span><span>/{{row.conversion}}{{row.contract_code.slice(0,-4)}}</span>
        </template>
       </el-table-column>
      <el-table-column label="冻结保证金" prop="init_margin" align="center" min-width="100px">
          <template slot-scope="{ row }">
          <span>{{ Number(row.margin).toFixed(6) }}</span>
        </template>
       </el-table-column>
      <el-table-column label="开仓均价" prop="price" align="center" min-width="90px"> </el-table-column>
      <el-table-column label="手续费" prop="commission" align="center" min-width="90px"> </el-table-column>
      
      <el-table-column label="强平价格" prop="force_price" align="center" min-width="90px"> </el-table-column>
      <!-- <el-table-column label="止盈价" prop="limit" align="center" min-width="90px"> </el-table-column> -->
      <!-- <el-table-column label="止损价" prop="stop" align="center" min-width="90px"> </el-table-column> -->
      <el-table-column label="浮动PNL" prop="float_profit" align="center" min-width="90px"> </el-table-column>
      <!-- <el-table-column label="IP地址" prop="ipaddress" align="center" min-width="100px"> </el-table-column> -->
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
//引入的自定义指令
import waves from "@/directive/waves";
//引入封装接口
import { docfollowposition } from "@/api/documentaryQuery";
import { bcprocontractset } from "@/api/user";

export default {
  name: "followholdquery",
  data() {
    return {
      listLoading: false,
      total: 0,
      holdList: null,
      contractOptions: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        account_type: null, //账户模式 3：跟单 4：带单
        contract_code: "", //合约代码
        side: null, //方向 B买S卖
        pageNo: 1,
        pagesize: 10,
      },
      accountTypeOptions: [
        { key: 3, name: '跟单' },
        { key: 4, name: '带单' },
      ],
      sizeOptions: [
        { key: "B", name: '买入开多' },
        { key: "S", name: '卖出开空' },
      ],
      downloadLoading: false,//导出加载中效果
    };
  },

  components: {},

  computed: {},

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.getList();
  },

  methods: {
    //  请求表格数据
    getList() {
      this.listLoading = true;
      this.listQuery.side = this.listQuery.side || undefined
      this.listQuery.account_type = this.listQuery.account_type || undefined
      docfollowposition(this.listQuery).then((response) => {
        this.holdList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //表格导出功能
    handleDownload() {
      this.downloadLoading = true;
      import("@/vendor/Export2Excel").then((excel) => {
        const tHeader = [
          "UID",
          "用户名",
          "顶级代理ID",
          "上级代理ID",
          "上机代理用户名",
          "合约",
          "仓位类型",
          "杠杆",
          "张数",
          "冻结保证金",
          "开仓均价",
          "手续费",
          "成交时间",
          "资金费用",
          "强平价格",
          "止盈价",
          "止损价",
          "浮动PNL",
          "IP地址",
        ];
        const filterVal = [
          "uid",
          "username",
          "top_id",
          "Superior_id",
          "top_username",
          "contract_type",
          "position_type",
          "lever",
          "sheets_number",
          "freeze_margin",
          "open_price",
          "handling_fee",
          "transaction_time",
          "funding_costs",
          "liquidation_price",
          "surplus_price",
          "damage_price",
          "float_profit_loss",
          "ip",
        ];
        const data = this.formatJson(filterVal, this.holdList);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: "table-list",
        });
        this.downloadLoading = false;
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          if (j === "transaction_time") {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
  },
};
</script>
<style lang="scss" scoped>
</style>