 <template>
  <div class="rebatelistdetail_wrap">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.topid"
        size="mini"
        placeholder="顶级代理ID"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 160px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">此IP交易次数≥</span>
       <el-input
        v-model.number="listQuery.trade_times"
        size="mini"
        placeholder="请输入"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        oninput="value=value.replace(/[^\d]/g,'')"
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        style="margin-left: 10px"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="rebateDetailList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="user_id" align="center" min-width="78">
        <template slot-scope="{row}">
          <span>{{ row.user_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户类型" min-width="105px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.user_type && userTypeOptions[row.user_type] || '--'}}</span>
        </template> 
      </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78">
       <template slot-scope="{row}">
          <span>{{row.top_agent_id || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级代理ID" prop="parent_agent_id" align="center" min-width="78">
       <template slot-scope="{row}">
          <span>{{row.parent_agent_id || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="注册方式" prop="register_method" align="center" min-width="95"/>
      <el-table-column label="注册时间" prop="created_time" align="center" min-width="75" />
      <el-table-column label="KYC状态" prop="verify" align="center" min-width="90px">
        <template slot-scope="{row}">
          <span>{{categoryStatus[row.verify]}}</span>
        </template>
      </el-table-column>
      <el-table-column label="此IP登录次数" prop="login_times_this" align="center" min-width="95px">
        <template slot-scope="{row}">
          <span class="canclick" @click="viewClick(row, 'two')">{{ row.login_times_this }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总登录次数" prop="login_times_total" align="center" min-width="90px">
        <template slot-scope="{row}">
          <span class="canclick" @click="viewClick(row, 'one')">{{ row.login_times_total }}</span>
        </template>
      </el-table-column>
      <el-table-column label="登录设备个数" prop="device_amount" align="center" min-width="90px" >
        <template slot-scope="{row}">
          <span class="canclick" @click="viewClick(row, 'one')">{{ row.device_amount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后登录时间" prop="last_login_time" align="center" min-width="75"/>
      <el-table-column label="此IP交易次数" prop="trade_times_this" align="center" min-width="90px"/>
      <el-table-column label="总交易次数" prop="trade_times_total" align="center" min-width="90px"/>
      <el-table-column label="最后交易时间" prop="last_trade_time" align="center" min-width="75">
        <template slot-scope="{row}">
          <span>{{ row.last_trade_time || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="80px">
        <template slot-scope="{ row }">
          <el-button type="primary" size="mini" @click="detailsClick(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="rebatelistDetail"
    />

    <el-dialog v-dialogDrag title="详情" :visible.sync="viewDialogVisible" width="80%">
      <el-table
        v-loading="viewlistLoading"
        :data="viewList"
        border
        fit
        highlight-current-row
      size="mini"
        style="width: 100%;"
        :header-cell-style="{ background: '#F0F8FF' }"
      >
        <el-table-column label="IP" prop="ip_address" align="center" min-width="120">
          <template slot-scope="{row}">
            <span>{{row.ip_address || '--'}}</span>
          </template>
        </el-table-column>
        <el-table-column label="设备" prop="device" align="center" min-width="90px"/>
        <el-table-column label="设备ID" prop="device_id" align="center" min-width="90px"/>
        <el-table-column label="操作系统" prop="os_type" align="center" min-width="90px">
          <template slot-scope="{row}">
            <span>{{os_typeObj[row.os_type]}}</span>
          </template>
        </el-table-column>
        <el-table-column label="语言" prop="lang_type" align="center" min-width="90px">
          <template slot-scope="{row}">
            <span>{{ langObj[row.lang_type] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="app版本" prop="version" align="center" min-width="90px">
          <template slot-scope="{row}">
            <span>{{row.version || '--'}}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="网络运营商" prop="contractcode" align="center" min-width="90px"/> -->
        <el-table-column label="操作时间" prop="created_time" align="center" min-width="90px"/>
      </el-table>
      <pagina-tion
         v-show="viewtotal > 0"
        :total="viewtotal"
        :page.sync="viewQuery.pageNo"
        :limit.sync="viewQuery.pagesize"
        @pagination="viewClicks"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ipuserlist, ipdetaillist } from "@/api/riskAdminister";

export default {
  name: "ipuserlist",
  data() {
    return {
      listLoading: false,

      rebateDetailList: null, //接受详情数据

      filterTime: [],
      listQuery: {
        ip_address: "",
        sname: "",
        topid: "",
        sagent: "",
        trade_times: undefined,
        pageNo: 1,
        pagesize: 10,
      },
      id: "",
      total: 0,
      //控制查看对话框
      viewDialogVisible: false,
      viewList: [],
      viewtotal: 0,
      viewlistLoading: false,
      viewQuery: {
        user_id: "", //用户id
        ip_address: "",
        pageNo: 1,
        pagesize: 10,
      },
      orderTypeObj: {
        0: "市价单",
        1: "计划单", 
        2: "止盈单", 
        4: "止损单", 
        5: "强平单"
      },
      status: {
        1: '待发放',
        2: '已发放',
        3: '已取消',
      },
      categoryStatus:{
        0: '未认证',
        1: '身份信息认证中',
        2: '身份信息认证失败',
        3: '身份信息认证通过',
        4: '人脸信息认证中',
        5: '人脸信息认证失败',
        6: '人脸信息认证通过',
        7: '重置',
      },
      userTypeOptions: {
        1: "顶级代理",
        2: "代理",
        3: "普通用户",
        4: "代理直推用户",
      },
      langObj:{
        0: '中文简体',
        1: '英文',
        2: '中文繁体',
        3: '韩语',
        4: '日语',
      },
      os_typeObj:{
        1: "android",
        2: "iOS",
        3: "WEB",
        4: "H5",
        5: "open_api",
        6: "系统自动",
      },
    };
  },

  components: {},

  activated(){
    this.rebatelistDetail();
  },

  computed: {},

  mounted() {
    this.rebatelistDetail();
  },

  methods: {
    rebatelistDetail() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, {
        ip_address: this.$route.query.ip,
        trade_times: this.listQuery.trade_times?Number(this.listQuery.trade_times):undefined
      });
      ipuserlist(data).then((res) => {
        this.rebateDetailList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.rebatelistDetail();
    },
    detailsClick(row){
      this.$router.push({
        path: "/user/detail",
        query: {
          id: row.user_id,
        },
      });
    },
    //查看对话框
    viewClick(row, type) {
      this.viewQuery.user_id = JSON.stringify(row.user_id);
      this.viewQuery.ip_address = type == 'one'? undefined : this.$route.query.ip;
      this.viewDialogVisible = true;
      this.viewClicks();
    },
    viewClicks() {
      this.viewlistLoading = true;
      let data = {}
      Object.assign(data, this.viewQuery);
      ipdetaillist(data).then((res) => {
        this.viewList = res.data.list;
        this.viewtotal = res.data.total;
        this.viewlistLoading = false;
      });
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = (val && val[1] + " 23:59:59") || "";
    },
  },
};
</script>
<style lang="scss" scoped>
// @import url('~@/styles/variables.scss');
.rebatelistdetail_wrap {
  width: 100%;
  .canclick{
    color: #409EFF;
    cursor: pointer;
  }
}
</style>