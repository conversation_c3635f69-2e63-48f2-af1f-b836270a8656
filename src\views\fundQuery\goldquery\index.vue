<template>
  <div class="gold-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID/昵称"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.coinid"
        placeholder="币种"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in coinOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyid"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.stype"
        placeholder="订单类型"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin-left: 20px; font-size: 12px">成交时间</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change='filterTimeTransform'
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin: 10px 0 0 20px;"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="goldList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column label="用户名" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78"> 
         <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="币种" prop="currencyname" align="center" min-width="80px"> </el-table-column>
      <el-table-column label="金额" prop="amount" min-width="90px" align="center"></el-table-column>
      <el-table-column label="钱包账户余额" prop="balance" min-width="120px" align="center"></el-table-column>
      <el-table-column label="订单类型" prop="netcash" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.type && stypeOptions.find((v)=>(v.key == row.type)) && stypeOptions.find((v)=>(v.key == row.type)).name || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="成交时间" prop="createdtime" width="75" align="center"></el-table-column>
     </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getwalletbill } from "@/api/fundQuery";
import { getprocoinList } from "@/api/user";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";

export default {
  name: "goldquery",
  data() {
    return {
      listLoading: false,
      total: 0,
      goldList: null,
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        coinid: '', //币种id -1全部
        star: "", //开始
        end: "", //结束
        stype: undefined, //1充值 2 提币 64 法币购买
        pageNo: 1,
        pagesize: 10,
      },
      coinOptions: [],
      stypeOptions: [
        { key: 1, name:'充值' },
        { key: 2, name:'提币' },
        { key: 64, name:'法币交易' },
        { key: 4096, name:'法币卖出' },
      ],
      downloadLoading: false,
    };
  },

  components: {},

  computed: {},

  mounted() {
    getprocoinList().then((res)=>{
      this.coinOptions = res.data.filter(v=>v.status)
    })
    this.getList();
  },

  methods: {
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      data.coinid = data.coinid || -1
      // status: this.listQuery.status === 0 ? 0 : this.listQuery.status || -1,
      data.stype = data.stype || undefined
      getwalletbill(data).then((res) => {
        this.goldList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val && val[1]+' 23:59:59' || '';
    },
  },
};
</script>
<style lang="scss" scoped>
</style>