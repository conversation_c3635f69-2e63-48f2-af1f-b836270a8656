<template>
  <div class="level-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID/昵称"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        placeholder="合约"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.account_type"
        placeholder="仓位类型"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        placeholder="方向"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.order_type"
        placeholder="交易类型"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in orderTypeArr"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <!-- <el-select
        size="mini"
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.key"
          :label="item.status_name"
          :value="item.key"
        />
      </el-select> -->
      <span style="margin-left: 20px; font-size: 12px">成交时间</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change='filterTimeTransform'
      >
      </el-date-picker>
      <el-input
        v-model="listQuery.ip"
        size="mini"
        placeholder="IP"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        size="mini"
        v-model="listQuery.tradeid"
        placeholder="交易编号"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px;"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('closetradeexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        导出
      </el-button>
    </div>

    <!-- <el-row>
      <el-col :span="18" style="border: 1px solid #c9c9c9; margin: 25px 10%"> -->
  
        <div class="box_se" v-if="$store.getters.roles.indexOf('closetradelistConfluence')>-1">
             <div class="protradepnl_main protradepnlVal">
              <div class="hei">净平仓PNL</div>
               <!-- <div :class="`hei ${(protradepnl.netpnl == 0 || protradepnl.netpnl == '--')?'':protradepnl.netpnl>0?'green':'red'}`"> -->
                <div v-show="protradepnl.calNetpnl != '--'" :class="`hei ${(Number(protradepnl.calNetpnl) == 0 || Number(protradepnl.calNetpnl) == '--')?'':Number(protradepnl.calNetpnl)>0?'green':'red'}`">
                  <span>
                    <!-- {{ (protradepnl.netpnl>0 && '+' || '') + protradepnl.netpnl }} -->
                     {{ (Number(protradepnl.calNetpnl)>0 && '+' || '') + Number(protradepnl.clospnl).sub(Math.abs(protradepnl.commission))}}
                  </span>
                </div>
                <span v-show="protradepnl.calNetpnl == '--'">--</span>
            </div>
            <div class="protradepnl_main protradepnlVal">
              <div class="hei">平仓PNL</div>
               <div :class="`hei ${(protradepnl.clospnl == 0 || protradepnl.clospnl == '--')?'':protradepnl.clospnl>0?'green':'red'}`">
                <span>{{ (protradepnl.clospnl>0 && '+' || '') + protradepnl.clospnl }}</span>
              </div>
            </div>
             <div class="protradepnl_main protradepnlVal">
              <div class="hei">手续费</div>
              <div :class="`hei ${(protradepnl.commission == 0 || protradepnl.commission == '--')?'':protradepnl.commission>0?'green':'red'}`">
                <span>{{ (protradepnl.commission>0 && '+' || '') + protradepnl.commission }}</span>
              </div>
            </div>
        </div>
       
      <!-- </el-col>
    </el-row> -->

    <el-table
      v-loading="listLoading"
      :data="levelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column label="用户名" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="90">
           <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="交易编号" prop="tradeid" align="center" min-width="78"> </el-table-column>
      <el-table-column label="合约" prop="contractcode" min-width="90px" align="center"></el-table-column>
      <el-table-column label="仓位类型" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.accounttype==1?'全仓':'逐仓'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="方向" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?'卖出平多':'买入平空'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="杠杆" align="center" min-width="60px">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="volume" align="center" min-width="110px"> 
        <template slot-scope="{ row }">
          <span>{{ row.volume }}张</span><span>/{{row.conversion}}{{row.contractcode.slice(0,-4)}}</span>
        </template>
      </el-table-column>
     <el-table-column label="开仓均价" prop="open_avg_price" align="center" min-width="90px"> </el-table-column>
      <el-table-column label="平仓价格" prop="price" align="center" min-width="90px"> </el-table-column>
      <el-table-column label="手续费" prop="commission" align="center" min-width="90px"> </el-table-column>
      <el-table-column label="净PNL" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ row.profit}}</span>
        </template>
      </el-table-column>
      <el-table-column label="PNL" prop="closeprofit" align="center" min-width="90px"> </el-table-column>
      <el-table-column label="交易类型" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ orderTypeObj[row.ordertype]  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="IP地址" prop="ipaddress" align="center" min-width="100px">>
        <template slot-scope="{ row }">
          <span>{{ row.ipaddress || '--'  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成交时间" prop="tradetime" align="center" min-width="75"> </el-table-column>
      <el-table-column label="开仓时间" prop="last_open_time" align="center" min-width="75"></el-table-column>
      <el-table-column label="订单来源" prop="imei" align="center" min-width="130px">
        <template slot-scope="{ row }">
          <!-- <span>{{orderTypeObj[row.ordertype]+'--'+os_typeObj[row.orderclient]}}</span> -->
          <span>{{row.fromType}}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[100,200,300,500]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { closetradelist,closetradeexport } from "@/api/transactionQuery";
import { bcprocontractset } from "@/api/user";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";

export default {
  name: "levelquery",
  data() {
    return {
      listLoading: false,
      total: 0,
      levelList: null,
      filterTime: [],
      listQuery: {
        tradeid: "", //
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        account_type: undefined, //账户模式 1：全仓 2：逐仓
        contract_code: "", //合约代码
        side: undefined, //方向 B买S卖
        order_type: "",  // 交易类型
        pageNo: 1,
        pagesize: 100,
        star: '', //开始
        end: '', //结束
        ip:""
      },
      contractOptions: [],
      accountTypeOptions: [
        { key: 1, name: '全仓' },
        { key: 2, name: '逐仓' },
      ],
      sizeOptions: [
        // { key: "B", name: '买入开多' },
        // { key: "S", name: '卖出开空' },
         { key: "B", name: '买入平空' },
        { key: "S", name: '卖出平多' },
      ],
      orderTypeArr:[
        {key: 0, name: "市价单"},
        {key: 1, name: "计划单"}, 
        {key: 2, name: "止盈单"}, 
        {key: 4, name: "止损单"}, 
        {key: 5, name: "强平单"},
      ],
      orderTypeObj: {
        0: "市价单",
        1: "计划单", 
        2: "止盈单", 
        4: "止损单", 
        5: "强平单"
      },
      os_typeObj:{
        0: 'open_api',
        1: "android",
        2: "iOS",
        3: "WEB",
        4: "H5",
        5: "open_api",
        6: "系统自动",
      },
      protradepnl: {
        netpnl: '--', //净netpnl
        clospnl: '--', //平仓pnl
        commission: '--', //手续费
        calNetpnl: '--', //计算净平仓
      },
      //总条数，默认为0
      exportLoading: false, //导出加载中效果
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList();
    },
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      data.side = this.listQuery.side || undefined
      data.account_type = this.listQuery.account_type || undefined
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
      data.order_type = data.order_type === ''?-1:data.order_type
      
      closetradelist(data).then((res) => {
        if(res.data.list && res.data.list.length){
          this.levelList = res.data.list.map((v)=>{
            // <span>{{orderTypeObj[row.ordertype]+'--'+os_typeObj[row.orderclient]}}</span>
            // orderclient ==> 1: android 2: iOS 3: WEB 4: H5 0: open_api 6: 系统自动
            // ordertype ==> 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单  
                // 带单平仓：带单交易员自主平仓所产生的跟单平仓
                // 带单止盈：带单交易员设置的止盈价触发后的平仓
                // 带单止损：带单交易员设置的止损价触发后的平仓
            // api平仓：用户通过api的平仓
            // 用户平仓：用户自主平仓
            // 止盈平仓：用户自己设置的止盈价触发后的平仓
            // 止损平仓：用户自己设置的止损价触发后的平仓
            // 系统平仓：如强制平仓等系统触发的平仓

            if(v.orderclient == 0 || v.orderclient == 5){
            // api
              if(v.ordertype == 2){
                v.fromType = '止盈平仓'
              }else if(v.ordertype == 4){
                v.fromType = '止损平仓'
              }else if(v.ordertype == 5){
                v.fromType = '系统平仓'
              }else{
                v.fromType = 'API平仓'
              }
            }else if(v.orderclient == 6){
            // 系统自动
              v.fromType = '系统平仓'
            }else{
            // 用户
              if(v.ordertype == 2){
                v.fromType = '止盈平仓'
              }else if(v.ordertype == 4){
                v.fromType = '止损平仓'
              }else{
                v.fromType = '用户平仓/'+this.os_typeObj[v.orderclient]
              }
            }
            return v
          });
        }else{
          this.levelList = []
        }
        this.total = res.data.total;
        let protradepnl = res.data.protradepnl
        Object.assign(this.protradepnl, protradepnl, {
          calNetpnl: Number(protradepnl.clospnl).sub(protradepnl.commission)
        })
        this.listLoading = false;
      });
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      data.side = this.listQuery.side || undefined
      data.account_type = this.listQuery.account_type || undefined
      data.order_type = data.order_type === ''?-1:data.order_type
      closetradeexport(data).then((res) => {
        if(res.ret == 0){
            // window.location.href=res.data.download_url;
          this.$notify.success({title:'操作成功',message:"请到‘交易查询/导出下载列表’进行下载"})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.box_se{
  border: 1px solid #c9c9c9;
  margin: 25px 10%;
  display:flex;
  flex-wrap: wrap;
} 
.protradepnl_main {
  display: flex;
  align-items: center;
  flex-direction: column;
  flex-wrap: wrap;
  width: 33%;
  .red {
    color: #DF334E;
  }
  .green {
    color: #309F72;
  }
  &>div{
    // width: 33.3%;
    text-align: center;
  }
}
.protradepnlKey{
  margin-top: 15px;
  margin-bottom: 5px;
}
.protradepnlVal{
  font-size: 18px;
  margin: 15px auto;
  // padding: 10px auto;
}
</style>