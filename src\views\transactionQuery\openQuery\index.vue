<template>
  <div class="open-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID/昵称"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        placeholder="合约"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.account_type"
        placeholder="仓位类型"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        placeholder="方向"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <!-- <el-select
        size="mini"
        v-model="listQuery.order_type"
        placeholder="交易类型"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in orderTypeArr"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select> -->
      <span style="margin-left: 20px; font-size: 12px">成交时间</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change='filterTimeTransform'
      >
      </el-date-picker>
      <el-input
        v-model="listQuery.ip"
        size="mini"
        placeholder="IP"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        size="mini"
        v-model="listQuery.tradeid"
        placeholder="交易编号"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px;"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('opentradeexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        导出
      </el-button>
    </div>


    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column label="用户名" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="交易编号" prop="tradeid" align="center" min-width="78"> </el-table-column>
      <el-table-column label="合约" prop="contractcode" min-width="100px" align="center"></el-table-column>
      <el-table-column label="仓位类型" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.accounttype==1?'全仓':'逐仓'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="方向" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?'卖出开空':'买入开多'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="杠杆" align="center" min-width="60px">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="volume" align="center" min-width="110px"> 
         <template slot-scope="{ row }">
          <span>{{ row.volume }}张</span><span>/{{row.conversion}}{{row.contractcode.slice(0,-4)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="开仓价" prop="price" align="center" min-width="90px"> </el-table-column>
       <!--<el-table-column label="平仓价格" prop="tradevalue" align="center" min-width="90px"> </el-table-column>-->
      <el-table-column label="手续费" prop="commission" align="center" min-width="90px"> </el-table-column>
     <!-- <el-table-column label="净PNL" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ Number(row.closeprofit).add(row.commission) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="PNL" prop="closeprofit" align="center" min-width="90px"> </el-table-column> -->
      <el-table-column label="交易类型" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ orderTypeObj[row.ordertype]  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="IP地址" prop="ipaddress" align="center" min-width="100px">>
        <template slot-scope="{ row }">
          <span>{{ row.ipaddress || '--'  }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成交时间" prop="tradetime" align="center" min-width="75"> </el-table-column>
      <el-table-column label="订单来源" prop="imei" align="center" min-width="130px">
        <template slot-scope="{ row }">
          <span>{{row.fromType}}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[100,200,300,500]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { opentradelist, opentradeexport } from "@/api/transactionQuery";
import { bcprocontractset } from "@/api/user";
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "openquery",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        tradeid: "", //
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        account_type: undefined, //账户模式 1：全仓 2：逐仓
        contract_code: "", //合约代码
        trade_id: "", //交易id
        side: undefined, //方向 B买S卖
        order_type: "",  // 交易类型
        pageNo: 1,
        pagesize: 100,
        star: '', //开始
        end: '', //结束
        ip:""
      },
      contractOptions: [],
      accountTypeOptions: [
        { key: 1, name: '全仓' },
        { key: 2, name: '逐仓' },
      ],
      sizeOptions: [
        { key: "B", name: '买入开多' },
        { key: "S", name: '卖出开空' },
      ],
      orderTypeArr:[
        {key: 0, name: "市价单"},
        {key: 1, name: "计划单"}, 
        {key: 2, name: "止盈单"}, 
        {key: 4, name: "止损单"}, 
        {key: 5, name: "强平单"},
      ],
      orderTypeObj: {
        0: "市价单",
        1: "计划单", 
        2: "止盈单", 
        4: "止损单", 
        5: "强平单"
      },
      os_typeObj:{
        1: "android",
        2: "iOS",
        3: "WEB",
        4: "H5",
        5: "open_api",
        6: "系统自动",
      },
      exportLoading: false, //导出加载中效果
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      data.side = this.listQuery.side || undefined
      data.account_type = this.listQuery.account_type || undefined
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
      data.order_type = data.order_type === ''?-1:data.order_type
      opentradelist(data).then((res) => {
        if(res.data.list && res.data.list.length){
          this.openList = res.data.list.map((v)=>{
            // <span>{{orderTypeObj[row.ordertype]+'--'+os_typeObj[row.orderclient]}}</span>
            // 委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
            // 0:市价单 1：计划单 2：止盈单 4：止损单 5：强平单  
                // 用户平仓：用户自主平仓
                // 带单平仓：带单交易员自主平仓所产生的跟单平仓
                // 止盈平仓：用户自己设置的止盈价触发后的平仓
                // 止损平仓：用户自己设置的止损价触发后的平仓
                // 系统平仓：如强制平仓等系统触发的平仓
                // 带单止盈：带单交易员设置的止盈价触发后的平仓
                // 带单止损：带单交易员设置的止损价触发后的平仓
                // api平仓：用户通过api的平仓

            // 带单开仓：带单交易员自主开仓所产生的跟单开仓
            // api开仓：用户通过api的开仓
            // 用户开仓：用户自主开仓
            // 计划委托：计划委托触发成交

            // API开仓
            if(v.orderclient == 0 || v.orderclient == 5){
              v.fromType = 'API开仓'
            }else if(v.orderclient == 6){
              v.fromType = '计划委托'
            }else{
              v.fromType = '用户开仓/'+this.os_typeObj[v.orderclient]
            }
            return v
          });
        }else{
          this.openList = []
        }
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      data.side = this.listQuery.side || undefined
      data.account_type = this.listQuery.account_type || undefined
      data.order_type = data.order_type === ''?-1:data.order_type
      opentradeexport(data).then((res) => {
        if(res.ret == 0){
          this.$notify.success({title:'操作成功',message:"请到‘交易查询/导出下载列表’进行下载"})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
  },
};
</script>
<style lang="scss" scoped>
</style>