import request from '@/utils/request'
//概览数据
export function getfinacewallet(data) {
  return request({
    url: '/managers/v1/platform/getfinacewallet',
    method: 'post',
    data: { data }
  })
}
//资产用户数据
export function platwalletlist(data) {
  return request({
    url: '/managers/v1/platform/platwalletlist',
    method: 'post',
    data: { data }
  })
}
//资产账户历史记录
export function platwalletdaillist(data) {
  return request({
    url: '/managers/v1/platform/platwalletdaillist',
    method: 'post',
    data: { data }
  })
}

//交易账户数据
export function getplataccount(data) {
  return request({
    url: '/managers/v1/platform/getplataccount',
    method: 'post',
    data: { data }
  })
}
//交易账户历史记录
export function plataccountdail(data) {
  return request({
    url: '/managers/v1/platform/plataccountdail',
    method: 'post',
    data: { data }
  })
}