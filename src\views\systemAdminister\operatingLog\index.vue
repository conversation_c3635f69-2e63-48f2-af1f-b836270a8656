<template>
  <div class="operating-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.uid"
        placeholder="UID/手机/邮箱"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        size="mini"
        v-model="listQuery.manage"
        placeholder="角色账号"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-right: 5px; font-size: 12px">操作时间</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 20px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change="filterTimeTransform"
      >
      </el-date-picker>
      <el-select
        size="mini"
        v-model="listQuery.stype"
        placeholder="操作类型"
        clearable
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="(value, key, idx) in typeOptions"
          :key="idx"
          :label="value"
          :value="key"
        />
      </el-select>
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="operatingList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column
        label="操作时间"
        prop="creat_time"
        align="center"
        min-width="110px"
      ></el-table-column>
      <el-table-column
        label="角色账号"
        prop="manage"
        align="center"
        min-width="90px"
      >
      </el-table-column>
      <el-table-column label="操作类型" align="center" min-width="130px">
        <template slot-scope="{ row }">
          <span>{{ typeOptions[row.stype] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作对象"
        prop="user_id"
        align="center"
        min-width="100px"
      >
      <template slot-scope="{ row }">
          <span>{{ row.user_id || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getmanagelogs } from "@/api/systemAdminister";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "operatingLog",
  data() {
    return {
      listLoading: false,
      operatingList: null,
      total: 0,
      filterTime: [],
      listQuery: {
        uid: "", //用户id
        manage: '', //角色账号
        stype: '',
        star: "",
        end: "",
        pageNo: 1,
        pagesize: 10,
      },
      typeOptions: {
        1: "修改标签",
        2: "添加代理",
        3: "修改用户信息",
        4: "添加备注",
        5: "添加标签",
        6: "添加标签信息",
        7: "身份审核",
        8: "审核提币",
        9: "修改标签信息",
        10: "发放返佣",
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    //渲染table数据
    getList() {
      this.listLoading = true;
      let data = {}
      getmanagelogs(Object.assign(data,this.listQuery,{
        stype: Number(this.listQuery.stype) || 0
      })).then((response) => {
        this.operatingList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
  },
};
</script>
<style lang="scss" scoped>
</style>