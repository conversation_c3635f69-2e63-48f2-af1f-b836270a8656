<template>
  <div class="open-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.user_id"
        placeholder="UID"
        style="width: 150px;margin-right: 20px;"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-right: 20px;"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>


    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="user_id" align="center" min-width="78"> </el-table-column>
      <el-table-column label="用户名" prop="user_name" align="center" min-width="110px">
        <template slot-scope="{row}">
          <span>{{row.user_name || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="110px">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户总入金" prop="in_cash" align="center" min-width="130"> </el-table-column>
      <el-table-column label="客户总出金" prop="out_cash" align="center" min-width="120"> </el-table-column>
      <el-table-column label="客户净入金" prop="net_income" align="center" min-width="120"> </el-table-column>
      <el-table-column label="客户合约账户权益" prop="account_equity" align="center" min-width="120"> </el-table-column>
      <el-table-column label="冻结保证金" prop="deposit" align="center" min-width="120"> </el-table-column>
      <el-table-column label="上周PNL" prop="last_wek_pnl" align="center" min-width="120"> </el-table-column>
      <el-table-column label="上周手续费" prop="last_wek_cmmis" align="center" min-width="120"> </el-table-column>
      <el-table-column label="历史PNL(截至上周日)" prop="his_pnl" align="center" min-width="120"> </el-table-column>
      <el-table-column label="当日PNL" prop="day_pnl" align="center" min-width="120"> </el-table-column>
      <el-table-column label="本周PNL(起始本周一)" prop="wek_pnl" align="center" min-width="120"> </el-table-column>
      <el-table-column label="本周手续费(起始本周一)" prop="trade_fee" align="center" min-width="120"> </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { getagentcapital } from "@/api/fundQuery";
export default {
  name: "agentCapital",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        user_id: undefined,
        pageNo: 1,
        pagesize: 10,
      },
      typeOptions: [
        {key: 0, name: "登录"},
        {key: 1, name: "注册"},
        {key: 2, name: "找回登录密码"},
        {key: 3, name: "登录密码设置"},
        {key: 4, name: "资金密码设置"},
        {key: 5, name: "用户名设置"},
        {key: 6, name: "提现申请"},
        {key: 7, name: "谷歌验证码设置"},
        {key: 8, name: "市价开仓"},
        {key: 9, name: "市价平仓"},
        {key: 10, name: "一键平仓"},
        {key: 11, name: "止盈止损"},
        {key: 12, name: "计划委托"},
        {key: 13, name: "计划单撤销"},
        {key: 14, name: "法币交易"},
        {key: 15, name: "绑定支付方式"},
        {key: 16, name: "获取验证码"},
        {key: 17, name: "KYC1申请"},
        {key: 18, name: "KYC2申请"},
        {key: 19, name: "人工KYC申请"},
      ],
      typeObj: {
        0:"登录",
        1:"注册",
        2:"找回登录密码",
        3:"登录密码设置",
        4:"资金密码设置",
        5:"用户名设置",
        6:"提现申请",
        7:"谷歌验证码设置",
        8:"市价开仓",
        9:"市价平仓",
        10:"一键平仓",
        11:"止盈止损",
        12:"计划委托",
        13:"计划单撤销",
        14:"法币交易",
        15:"绑定支付方式",
        16:"获取验证码",
        17:"KYC1申请",
        18:"KYC2申请",
        19:"人工KYC申请",
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      // let data = {};
      // Object.assign(data, this.listQuery, {
      //   stype: this.listQuery.stype === 0 ? 0 : this.listQuery.stype || undefined,
      // });
      getagentcapital(this.listQuery).then((res) => {
        this.openList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
  },
};
</script>
<style lang="scss" scoped>
</style>