import request from '@/utils/request'

/**获取banner列表*/
export function getbcprobannerlist(data) {
  return request({
    url: '/managers/v1/banner/bcprobannerlist',
    method: 'post',
    data: { data }
  })
}
/**上传banner title，image返回的中文图片，imageen返回的英文图片，link中文跳转地址，linken英文跳转地址*/
export function bcprobanneradd(data) {
  return request({
    url: '/managers/v1/banner/bcprobanneradd',
    method: 'post',
    data: { data }
  })
}
/**删除/排序banner*/
export function bcprobannerup(data) {
  return request({
    url: '/managers/v1/banner/bcprobannerup',
    method: 'post',
    data: { data }
  })
}
//系统管理、角色管理table数据接口
export function managelist(data) {
  return request({
    url: '/managers/v1/manager/managelist',
    method: 'post',
    data: { data }
  })
}
// 修改账户分组
export function managesave(data) {
  return request({
    url: '/managers/v1/manager/managesave',
    method: 'post',
    data: { data }
  })
}
// 添加账户
export function manageadd(data) {
  return request({
    url: '/managers/v1/manager/manageadd',
    method: 'post',
    data: { data }
  })
}
// 删除账户
export function managedel(data) {
  return request({
    url: '/managers/v1/manager/managedel',
    method: 'post',
    data: { data }
  })
}
//权限分组
export function moldelist(data) {
  return request({
    url: '/managers/v1/manager/moldelist',
    method: 'post',
    data: { data }
  })
}
//添加分组
export function moldeladd(data) {
  return request({
    url: '/managers/v1/manager/moldeladd',
    method: 'post',
    data: { data }
  })
}
//删除分组
export function moldeldel(data) {
  return request({
    url: '/managers/v1/manager/moldeldel',
    method: 'post',
    data: { data }
  })
}
//分组详情
export function moldelview(data) {
  return request({
    url: '/managers/v1/manager/moldelview',
    method: 'post',
    data: { data }
  })
}
//操作日志
export function getmanagelogs(data) {
  return request({
    url: '/managers/v1/manager/getmanagelogs',
    method: 'post',
    data: { data }
  })
}
//CRM操作日志
export function getagentoplog(data) {
  return request({
    url: '/managers/v1/period/getagentoplog',
    method: 'post',
    data: { data }
  })
}
//联系我们
export function getcontactuslist(data) {
  return request({
    url: '/managers/v1/notice/getcontactuslist',
    method: 'post',
    data: { data }
  })
}
//用户日志统计
export function getuserlog(data) {
  return request({
    url: '/managers/v1/user/getuserlog',
    method: 'post',
    data: { data }
  })
}
//用户日志统计-导出
export function getuserlogexport(data) {
  return request({
    url: '/managers/v1/user/getuserlogexport',
    method: 'post',
    data: { data }
  })
}
//用户错误反馈数据
export function usererrlog(data) {
  return request({
    url: '/managers/v1/user/usererrlog',
    method: 'post',
    data: { data }
  })
}
