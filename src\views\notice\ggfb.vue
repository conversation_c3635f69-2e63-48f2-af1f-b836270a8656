<!--公告发布-->
<template>
  <div class="withdraw">
    <div class="w_c">
      <div class="article_title">
        <span>文章标题：</span>
        <el-input style="width:280px;" v-model="articleTitle" placeholder="请输入内容"></el-input>
        <span class="zhuyi_span">最多输入28个字符，最少输入4个字符；</span>
        <span style="padding-left: 20px;">语言类型：</span>
        <el-select v-model="articleLang" placeholder="请选择语言类型">
          <el-option
            v-for="item in langOptions"
            :key="item.value"
            :label="item.name"
            :value="item.value">
          </el-option>
        </el-select>
        <el-checkbox style="margin-left: 20px" v-model="is_important" :true-label="1" :false-label="0">是否开启弹框显示</el-checkbox>
      </div>
      <div class="article_content">
        <p>正文：</p>
        <div style=" width:100%;">
          <!-- 图片上传组件辅助-->
          <el-upload
            v-show="false"
            id="avatar-uploader"
            :action="actionUrl"
            name="file"
            ref="uploaden"
            :show-file-list="false"
            :on-success="uploadSuccess"
            :on-error="uploadError"
            :before-upload="beforeUpload"
            enctype="multipart/form-data"
            :data="uploadData"
          ></el-upload>
            <!-- action="https://jsonplaceholder.typicode.com/posts/" -->


          <!--富文本编辑器组件-->
          <el-row v-loading="quillUpdateImg">
            <quill-editor v-model="detailContent" ref="myQuillEditor" :options="editorOption"></quill-editor>
          </el-row>
        </div>
        <div class="handle_div">
          <!-- <el-button class="buttons" @click="cancel()">取消</el-button> -->
          <el-button type="primary" class="buttons" @click="entry()">发布</el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { quillEditor } from "vue-quill-editor"; //调用编辑器
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
// 工具栏配置
const toolbarOptions = [
  ["bold", "italic", "underline", "strike"], // toggled buttons
  ["blockquote", "code-block"],

  [{ header: 1 }, { header: 2 }], // custom button values
  [{ list: "ordered" }, { list: "bullet" }],
  [{ script: "sub" }, { script: "super" }], // superscript/subscript
  [{ indent: "-1" }, { indent: "+1" }], // outdent/indent
  [{ direction: "rtl" }], // text direction

  [{ size: ["small", false, "large", "huge"] }], // custom dropdown
  [{ header: [1, 2, 3, 4, 5, 6, false] }],

  [{ color: [] }, { background: [] }], // dropdown with defaults from theme
  [{ font: [] }],
  [{ align: [] }],
  ["link", "image", "video"],
  ["clean"] // remove formatting button
];
import { Notification } from "element-ui";
import { getAddupNotice } from "@/api/announcement";
export default {
  data() {
    return {
      quillUpdateImg: false,
      detailContent: "", // 富文本内容
      editorOption: {
        placeholder: "",
        theme: "snow", // or 'bubble'
        modules: {
          toolbar: {
            container: toolbarOptions, // 工具栏
            handlers: {
              image: function(value) {
                if (value) {
                  document.querySelector("#avatar-uploader input").click();
                } else {
                  this.quill.format("image", false);
                }
              }
            }
          }
        }
      }, // 富文本编辑器配置

      datatotal: 0, //总数据条数
      currentPageNum: 10, //每页默认10条
      currentPage: 1, //当前页

      detailContent: "", // 富文本内容
      articleTitle: "", //文章标题
      actionUrl:'',
      uploadData:{},
      disabledFB: false,
      articleLang: '',
      is_important: 0, // 是否开启弹框显示
      langOptions: [
        { name: '中文简体公告',  value: "0",},
        { name: '英文公告',  value: "1",},
        { name: '中文繁体公告',  value: "2",},
        { name: '韩文公告',  value: "3",},
        { name: '日文公告',  value: "4",},
      ], // 语言tab
    };
  },
  mounted() {
    // this.$register(this);
    // this.msg = [GETXGZJGG];
    this.actionUrl = 
      (process.env.VUE_APP_API == '/' ? window.location.protocol+'//'+window.location.host : process.env.VUE_APP_API)+ "/managers/v1/banner/bcprobannerupload";
  },
  components: {
    quillEditor
  },
  computed: {},
  methods: {
    cancel() {},
    entry() {
      if (!this.articleTitle) {
        this.$notify.warning({title:'提示',message: '请填写文章标题'})
        return false;
      }
      if (!this.articleLang) {
         this.$notify.warning({title:'提示',message: '请选择公告的对应语言类型'})
        return false;
      }
      // let res = /^[\u4e00-\u9fff]{4,28}$/   !res.test(this.articleTitle)
      if (this.articleTitle.length > 28 || this.articleTitle.length < 4) {
         this.$notify.warning({title:'提示',message: '文章标题请输入4-28个字符'})
        return false;
      }
      if (!this.detailContent) {
         this.$notify.warning({title:'提示',message: '请填写文本内容'})
        return false;
      }
      this.$confirm("是否添加该公告?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.getData();
        })
        .catch(() => {
         this.$message.error('已取消发布')
        });
    },

    getData() {
     var  data = {
          type: 1,
          title: this.articleTitle,
          abstract: "",
          content: this.detailContent,
          content_en: "",
          abstract_en: "",
          title_en: "",
          id: 0,
          lang_type: Number(this.articleLang),
          is_important: this.is_important, 
      }
      // console.log(data)
      getAddupNotice(data).then(data=>{
        // console.log(data)
          if (data.ret == 0) {
             this.$notify({
                  title: "成功",
                  message: "发布成功",
                  type: "success",
                  duration: 2000,
                });
            setTimeout(()=>{
              this.$router.push("/notice/gglb");
            },500)
          }else{
                this.$notify({
                  title: "失败",
                  message: "发布失败",
                  type: "warning",
                  duration: 2000,
                });
          }
      })
      // this.$api.getAddupNotice({
      //   tag: {
      //     dataType: GETXGZJGG,
      //     type: 1,
      //     title: this.articleTitle,
      //     abstract: "",
      //     content: this.detailContent,
      //     content_en: "",
      //     abstract_en: "",
      //     title_en: "",
      //     id: 0
      //   }
      // });
    },

    // message(tag, data) {
    //   switch (tag.dataType) {
    //     case GETXGZJGG:
    //       if (data.ret == 0) {
    //         this.$msg({
    //           title: "成功",
    //           message: "发布成功",
    //           type: "success"
    //         });
    //         this.$router.push("/notice/gglb");
    //       }
    //       break;
    //   }
    // },

    // 上传图片前
    beforeUpload(res, file) {
        let time = parseInt(new Date().getTime() / 1000) + "";
          let sin = md5(
            // md5("#$"+process.env.VUE_APP_APIKEY) + time
            md5(process.env.VUE_APP_APIKEY) + time
          );
            // console.log(sin)
          this.uploadData["sign"] = sin;
          this.uploadData["ts"] = time;
          // if (this.$refs.uploadzh.uploadFiles.length > 0) {
            // if (this.beforeAvatarUpload(this.$refs.uploadzh.uploadFiles[0])) {
          //     if (this.$refs.uploaden.uploadFiles.length > 0) {
          //       if (
          //         this.beforeAvatarUpload(this.$refs.uploaden.uploadFiles[0])
          //       ) {
          //         this.uploading = true;
          //         this.$refs.uploadzh.submit();
          //       } else {
          //         this.$refs.uploaden.clearFiles();
          //       }
          //     } else {
          //       this.uploading = true;
          //       this.$refs.uploadzh.submit();
          //     }
          //   } else {
          //     this.$refs.uploadzh.clearFiles();
          //   }
          // } else if (this.$refs.uploaden.uploadFiles.length > 0) {
          //   if (this.beforeAvatarUpload(this.$refs.uploaden.uploadFiles[0])) {
          //     this.uploading = true;
          //     this.$refs.uploaden.submit();
          //   }
          // } else {
          //   this.$message.error('请选择需要上传的图片')
          // }
      console.log("上传图片前");
      // 显示loading动画
      this.quillUpdateImg = true;
    },
    // 上传图片成功
    uploadSuccess(res, file) {
      console.log("上传图片成功");
      var self = this;
      // res为图片服务器返回的数据
      // 获取富文本组件实例
      let quill = self.$refs.myQuillEditor.quill;
      // console.log(quill);
      // console.log(res)
      // 如果上传成功
      if (res.ret === 0) {
        // 获取光标所在位置
        let length = quill.getSelection().index;
        // console.log(length)
        // 插入图片  res.info为服务器返回的图片地址
        // quill.insertEmbed(length, "image", self.$Api.imgUrlBase + res.result); self.actionUrl+'/'+res.data
        quill.insertEmbed(length, "image", self.actionUrl+res.data);
        console.log(res.data , quill)

        // 调整光标到最后
        quill.setSelection(length + 1);
      } else {
          this.$notify({
                  title: "错误",
                  message: "图片插入失败",
                  type: "warning",
                  duration: 2000,
                });
      }
      // loading动画消失
      this.quillUpdateImg = false;
    },
    // 上传图片失败
    uploadError(res, file) {
      console.log("上传图片失败");
      console.log(res);
      // loading动画消失
      this.quillUpdateImg = false;
               this.$notify({
                  title: "错误",
                  message: "图片插入失败",
                  type: "warning",
                  duration: 2000,
                });
    }
  },

};
</script>

<style lang="scss" scoped>
.buttons {
  margin-left: 30px;
}
       
.withdraw {
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .w_c {
    margin-top: 20px;
    width: 95%;
    flex-grow: 1;
    box-sizing: border-box;
    display: flex;
    align-self: center;
    align-items: flex-start;
    flex-direction: column;
    justify-content: flex-start;
    .article_title {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      white-space: nowrap;
      width: 100%;
      .zhuyi_span {
        font-size: 14px;
        padding-left: 10px;
      }
    }
    .article_content {
      display: flex;
      justify-content: flex-start;
      flex-direction: column;
      align-items: flex-start;
      width: 100%;
      p {
        margin: 10px 0 5px;
      }
     
       ::v-deep .quill-editor .ql-container {
          min-height: 350px !important;
          ::v-deep .ql-editor{
            min-height: 350px !important;
          }
        }
    }
    .handle_div {
      width: 100%;
      padding-top: 10px;
      text-align: right;
      .el-button{
        margin: 0;
      }
    }
  }
  .w_page {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-top: 10px;
    padding-right: 30px;
  }
}

.dialogc {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.dialogstip {
  color: red;
  padding: 20px 10px;
}
</style>
