<template>
  <div class="financial-container">
    <el-tabs v-model="tabActive" @tab-click="handleTab">
      <el-tab-pane label="钱包账户财务记录" name="first"></el-tab-pane>
      <el-tab-pane label="合约账户财务记录" name="second"></el-tab-pane>
      <el-tab-pane label="跟单账户财务记录" name="follow"></el-tab-pane>
    </el-tabs>
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID/昵称"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.coinid"
        placeholder="币种"
        clearable
        style="width: 120px; margin-right: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in coinOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyid"
        />
      </el-select>
      <el-select
        v-if="tabActive == 'first'"
        size="mini"
        v-model="listQuery.stype"
        placeholder="订单类型"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stypeOptions1"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
       <el-select
         v-else-if="tabActive == 'second'"
        size="mini"
        v-model="listQuery.stype"
        placeholder="订单类型"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stypeOptions2"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
        <el-select
         v-else-if="tabActive == 'follow'"
        size="mini"
        v-model="listQuery.stype"
        placeholder="订单类型"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stypeOptions3"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      
      <span style="margin-right: 20px; font-size: 12px">成交时间</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 20px; "
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change="filterTimeTransform"
      />
      <el-input
        v-show="tabActive == 'first'"
        v-model.number="listQuery.bill_id"
        size="mini"
        placeholder="成交记录"
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="financiaList"
      border
      fit
      size="mini"
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="userid" align="center" min-width="78">
      </el-table-column>
      <el-table-column
        label="用户名"
        prop="user_name"
        align="center"
        min-width="95"
      >
      </el-table-column>
      <el-table-column
        label="顶级代理ID"
        prop="top_agent_id"
        align="center"
        min-width="78"
      >
      <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || "--" }}</span>
      </template>
      </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="上级ID"
        prop="pareid"
        align="center"
        min-width="78"
      >
       <template slot-scope="{ row }">
          <span>{{ row.pareid || "--" }}</span>
      </template>
      </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tabActive == 'first'"
        label="成交编号"
        prop="billid"
        align="center"
        min-width="78"
      >
       <template slot-scope="{ row }">
          <span>{{ row.billid || "--" }}</span>
      </template>
      </el-table-column>
      <el-table-column
        label="币种"
        prop="currencyname"
        align="center"
        min-width="80px"
      >
      </el-table-column>
      <el-table-column
        label="金额"
        prop="amount"
        min-width="90px"
        align="center"
      ></el-table-column>
      <el-table-column
        :label="tabActive=='first'?'钱包账户余额': tabActive=='second'? '合约账户余额':'跟单账户余额'"
        prop="balance"
        min-width="120px"
        align="center"
      ></el-table-column>
       
      <el-table-column
        :label="`${tabActive == 'first'?'冻结':'可用'}数量`"
        :prop="tabActive == 'first'?'lockamount':'available'"
        min-width="120px"
        align="center"
      ></el-table-column>
      <el-table-column
        v-if="tabActive == 'first'"
        label="订单类型"
        prop="netcash"
        min-width="130px"
        align="center"
      >
        <template slot-scope="{ row }">
          <span>{{
            (row.type && stypeOptions1.find((v) => v.key == row.type) && stypeOptions1.find((v) => v.key == row.type).name) ||
            "--"
          }}</span>
        </template>
      </el-table-column>
       <el-table-column
        v-else-if="tabActive == 'second'"
        label="订单类型"
        prop="netcash"
        min-width="130px"
        align="center"
      >
        <template slot-scope="{ row }">
          <span>{{
            (row.type && stypeOptions2.find((v) => v.key == row.type) && stypeOptions2.find((v) => v.key == row.type).name) || "--"
          }}</span>
        </template>
      </el-table-column>
       <el-table-column
        v-else-if="tabActive == 'follow'"
        label="订单类型"
        prop="netcash"
        min-width="130px"
        align="center"
      >
        <template slot-scope="{ row }">
          <span>{{
            (row.type && stypeOptions3.find((v) => v.key == row.type) && stypeOptions3.find((v) => v.key == row.type).name) || "--"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="时间"
        prop="createdtime"
        width="75"
        align="center"
      ></el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[300,100,50,10]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getwallerhistory, getacchistory , followhistory } from "@/api/fundQuery";
import { getprocoinList } from "@/api/user";

export default {
  name: "financialrecords",
  data() {
    return {
      tabActive: 'first',
      listLoading: false,
      total: 0,
      financiaList: null,
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        coinid: null, //币种id -1全部
        star: "", //开始
        end: "", //结束
        stype: undefined, //1：充值  2：提现  4：划转到交易账户 8：从交易账户转入 16-邀请佣金奖励 32-代理佣金奖励 64-法币订单到账,128 空投 256-资产账户划转到跟单账户  512-跟单账户划转到资产账户  1024:佣金收入
        bill_id: null, //成交编号
        pageNo: 1,
        pagesize: 10,
      },
      coinOptions: [],
      stypeOptions1: [
        { key: 1, name:'充值' },
        { key: 2, name:'提币' },
        { key: 4, name:'划转到交易账户' },
        { key: 8, name:'划转到资产账户' },
        { key: 16, name:'邀请佣金奖励' },
        { key: 32, name:'代理佣金奖励' },
        { key: 64, name:'法币订单到账' },
        { key: 128, name:'空投奖励' },
        { key: 256, name:'资产账户划转到跟单账户' },
        { key: 512, name:'跟单账户划转到资产账户' },
        { key: 1024, name:'佣金收入' },
        { key: 2048, name:'活动奖励' },
      ],
      stypeOptions2: [
        { key: 1, name:'开仓手续费' },
        { key: 2, name:'资金费用' },
        { key: 4, name:'转入(从资产账户)' },
        { key: 8, name:'转出(到资产账户)' },
        { key: 16, name:'平仓盈亏' },
        { key: 32, name:'平仓手续费' },
        { key: 64, name:'模拟盘补充资产' },
        { key: 128, name:'调整保证金' },
        { key: 256, name:'预扣佣金' },
        { key: 512, name:'佣金退款' },
        { key: 1024, name:'佣金收入' },
        { key: 2048, name:'交易账户划转到跟单账户' },
        { key: 4096, name:'跟单账户划转到交易账户' },
        { key: 8192, name:'资产账户划转到跟单账户' },
        { key: 16384, name:'跟单账户划转到资产账户' },
        { key: 36768, name:'强平退回' },
        { key: 65536, name:'模拟盘减少资产' },
        { key: 131072, name:'爆仓清算费用' },
      ],
       stypeOptions3: [
        { key: 1, name:'开仓手续费' },
        { key: 2, name:'资金费用' },
        { key: 4, name:'转入(从资产账户)' },
        { key: 8, name:'转出(到资产账户)' },
        { key: 16, name:'平仓盈亏' },
        { key: 32, name:'平仓手续费' },
        { key: 64, name:'模拟盘补充资产' },
        { key: 128, name:'调整保证金' },
        { key: 256, name:'预扣佣金' },
        { key: 512, name:'佣金退款' },
        { key: 1024, name:'佣金收入' },
        { key: 2048, name:'交易账户划转到跟单账户' },
        { key: 4096, name:'跟单账户划转到交易账户' },
        { key: 8192, name:'资产账户划转到跟单账户' },
        { key: 16384, name:'跟单账户划转到资产账户' },
        { key: 36768, name:'强平退回' },
        { key: 65536, name:'模拟盘减少资产' },
        { key: 131072, name:'爆仓清算费用' },
      ],
    };
  },

  components: {},

  computed: {},

  mounted() {
    getprocoinList().then((res)=>{
      this.coinOptions = res.data.filter(v=>v.status)
    })
    this.getList();
  },

  methods: {
    handleTab(tab, event) {
      this.listQuery.sname = ""; //用户id,手机号，邮箱
      this.listQuery.sectop = ""; //顶级代理id或昵称
      this.listQuery.sagent = ""; //代理id或者名字
      this.listQuery.coinid = null; //币种id -1全部
      this.listQuery.star = ""; //开始
      this.listQuery.end = ""; //结束
      this.listQuery.stype = undefined; //1：充值  2：提现  4：划转到交易账户 8：从交易账户转入 16-邀请佣金奖励 32-代理佣金奖励 64-法币订单到账,128 空投 256-资产账户划转到跟单账户  512-跟单账户划转到资产账户  1024:佣金收入
      this.listQuery.bill_id = null;//成交编号
      this.listQuery.pageNo = 1;
      this.getList();
    },
    getList() {
      this.listLoading = true;
      let data = {}
      if(this.tabActive == 'first'){
        Object.assign(data,this.listQuery)
        data.coinid = data.coinid || -1
        data.bill_id = data.bill_id || undefined
        data.stype = data.stype || undefined
        getwallerhistory(data).then((response) => {
          this.financiaList = response.data.list;
          this.total = response.data.total;
          this.listLoading = false;
        });
      }else if(this.tabActive == 'second'){
        Object.assign(data,this.listQuery)
        data.coinid = data.coinid || -1
        data.bill_id = 0
        data.stype = data.stype || undefined
        getacchistory(data).then((response) => {
          this.financiaList = response.data.list;
          this.total = response.data.total;
          this.listLoading = false;
        });
      }
      else if(this.tabActive == 'follow'){
         Object.assign(data,this.listQuery)
        data.coinid = data.coinid || -1
        data.bill_id = 0
        data.stype = data.stype || undefined
        followhistory(data).then((response) => {
          this.financiaList = response.data.list;
          this.total = response.data.total;
          this.listLoading = false;
        });
      }
    },
    // 搜索事件
    handleFilter(){
      this.listQuery.page = 1;
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val && val[1]+' 23:59:59' || '';
    },
  },
};
</script>
<style lang="scss" scoped>
.financial-container {
  padding-top: 10px;
  &::v-deep .el-tabs{
    margin-left: 20px;
  }
}
</style>