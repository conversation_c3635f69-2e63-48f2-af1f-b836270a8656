<template>
  <div class="lebeladminister-container">
    <div class="filter-container">
      <el-button
        v-if="$store.getters.roles.indexOf('bcprocontractcoinadd') > -1"
        type="primary"
        style="margin-left: 30%; margin-bottom: 10px; float: right"
        @click="AddClick()"
        >添加标签</el-button
      >
    </div>

    <el-table
      v-loading="listLoading"
      :data="lebelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column
        label="标签名称"
        prop="label_name"
        align="center"
        min-width="100px"
      />
      <el-table-column
        label="合约"
        prop="contract_code"
        align="center"
        min-width="95px"
      />
      <el-table-column
        label="最大杠杆"
        prop="max_lever"
        align="center"
        min-width="90px"
      />
      <el-table-column
        label="单笔最大下单量"
        prop="max_order_volume"
        align="center"
        min-width="120"
      />
      <el-table-column
        label="单笔最小下单量"
        prop="min_order_volume"
        align="center"
        min-width="120"
      />
      <el-table-column
        label="最大持仓"
        prop="max_posi_volume"
        align="center"
        min-width="100px"
      />
      <el-table-column
        label="最小点差"
        prop="min_slippage"
        min-width="90px"
        align="center"
      />
      <el-table-column
        label="最大点差"
        prop="slippage"
        min-width="90px"
        align="center"
      />
      <el-table-column
        label="手续费率"
        prop="fee"
        min-width="90px"
        align="center"
      />
      <el-table-column
        label="资金费率"
        prop="funding"
        align="center"
        min-width="90"
      />
      <el-table-column
        label="最低风险率增减"
        prop="risk_rate"
        align="center"
        min-width="120"
      />
      <el-table-column label="状态" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.status_stype ? "开启" : "关闭" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        prop="creat_time"
        align="center"
        width="160px"
      />
      <el-table-column
        v-if="$store.getters.roles.indexOf('bcprocontractcoinadd') > -1"
        label="操作"
        min-width="220"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-button size="mini" @click="handleEdit(row)">编辑</el-button>
          <el-button
            size="mini"
            :type="row.status_stype ? 'info' : 'success'"
            @click="handleModifyStatus(row)"
            >{{ row.status_stype ? "关闭" : "开启" }}
          </el-button>
          <el-button type="danger" size="mini" @click="handleDel(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <el-dialog
      @close="closeConfigLabelDialog"
      :title="this.handleDialogType == 'add' ? '添加标签' : '编辑标签'"
      :visible.sync="addAndConfigDialogVisible"
      width="75%"
      v-dialogDrag
    >
      <el-form
        ref="selectLabelForm"
        :rules="rules"
        :model="selectLabelForm"
        label-width="auto"
        label-position="left"
      >
        <el-form-item label="选择标签" label-position="left" prop="lablname">
          <div style="display: flex; align-items: center">
            <el-select
              v-model="selectLabelForm.lablname"
              placeholder="请选择标签"
              style="width: 100%"
              :disabled="handleDialogType == 'edit'"
            >
              <el-option
                v-for="item in labelOptions"
                :key="item.labelid"
                :label="item.label_name"
                :value="item.labelid"
              ></el-option>
            </el-select>
            <i
              v-show="handleDialogType == 'add'"
              @click="
                () => {
                  addLableDialog = true;
                }
              "
              class="el-icon-circle-plus-outline"
              style="font-size: 24px; padding-left: 10px"
            ></i>
          </div>
        </el-form-item>
        <!-- <el-divider></el-divider>
        <el-button style="width: 90%; margin-left: 5%">设置其他合约</el-button> -->
      </el-form>
      <el-form
        v-show="selectLabelForm.lablname"
        ref="setLabelInfoForm"
        :rules="rules"
        :model="setLabelInfoForm"
        label-width="auto"
        label-position="left"
      >
        <el-divider></el-divider>
        <el-form-item label="选择合约" prop="contract_code">
          <el-select
            v-model="setLabelInfoForm.contract_code"
            placeholder="请选择合约"
            style="width: 100%"
            @change="contractChange"
            :disabled="handleDialogType == 'edit'"
          >
            <el-option
              v-for="item in contractOptions"
              :key="item.traderpairs"
              :label="item.traderpairs"
              :value="item.traderpairs"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="最大杠杆" prop="max_lever">
          <el-input
            v-model="setLabelInfoForm.max_lever"
            onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
          ></el-input>
        </el-form-item>
        <el-form-item label="单笔最大下单量" prop="max_order_volume">
          <el-input
            v-model="setLabelInfoForm.max_order_volume"
            onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
          >
            <i slot="suffix" style="font-style: normal">张</i>
          </el-input>
          <!-- <span style="color:#FF9900;font-size:13px;">最低风险率最大为0.25</span> -->
        </el-form-item>
        <el-form-item label="单笔最小下单量" prop="min_order_volume">
          <el-input
            v-model="setLabelInfoForm.min_order_volume"
            onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
          >
            <i slot="suffix" style="font-style: normal">张</i>
          </el-input>
        </el-form-item>
        <el-form-item label="最大持仓量" prop="max_posi_volume">
          <el-input
            v-model="setLabelInfoForm.max_posi_volume"
            onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
          >
            <i slot="suffix" style="font-style: normal">张</i>
          </el-input>
        </el-form-item>
        <el-form-item label="点差">
          <el-col :span="11">
            <el-form-item prop="min_slippage">
              <el-input v-model="setLabelInfoForm.min_slippage" oninput="value=value.replace(/[^\d^\.]/g,'')"></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align:center;" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="slippage">
              <el-input v-model="setLabelInfoForm.slippage" oninput="value=value.replace(/[^\d^\.]/g,'')"></el-input>
            </el-form-item>
          </el-col>
          <!-- <span style="color:#FF9900;font-size:13px;">点差最大可输入{{this.forms}}倍最小交易单位</span> -->
        </el-form-item>
        <el-form-item label="手续费率" prop="fee">
          <el-input v-model="setLabelInfoForm.fee" @input="feeInput"></el-input>
          <!--onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" 中间有个d{0,2}是输入限制-->
          <!-- <span style="color:#FF9900;font-size:13px;">手续费率最大可输入0.0005</span> -->
        </el-form-item>
        <el-form-item label="资金费率" prop="funding">
          <el-input v-model="setLabelInfoForm.funding"></el-input>
          <!-- <span style="color:#FF9900;font-size:13px;">资金费率最大可输入0.001</span> -->
        </el-form-item>
        <el-form-item label="最低风险率增减" prop="risk_rate">
          <el-input
            v-model="setLabelInfoForm.risk_rate"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
          ></el-input>
          <!-- <span style="color:#FF9900;font-size:13px;">最低风险率最大为0.25</span> -->
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="configLabelEntry()"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      @close="addLabelResetFields"
      class
      title="添加标签"
      width="50%"
      :visible.sync="addLableDialog"
      :close-on-click-modal="false"
    >
      <el-form
        :rules="rules"
        label-position="left"
        ref="addLabelForm"
        :model="addLabelForm"
        label-width="auto"
      >
        <el-form-item label="标签" prop="addLabelVal">
          <el-input v-model="addLabelForm.addLabelVal"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="addLableDialog = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="entryAddLabel()"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      @close="closexgmm"
      class
      title="输入谷歌二维码"
      width="350px"
      :visible.sync="checkvkeyDialog"
      :close-on-click-modal="false"
      @submit.native.prevent
    >
      <el-form
        :rules="rules"
        label-position="left"
        ref="ruleForm"
        :model="ruleForm"
        label-width="auto"
      >
        <el-form-item label="验证码" prop="yzmVal">
          <el-input v-model="ruleForm.yzmVal" maxlength="6"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="checkvkeyDialog = false"
          >取 消</el-button
        >
        <el-button size="mini" type="primary" @click="entrySendyzm()"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getlablinfo,
  addlabel,
  addlablinfo,
  updatelablinfo,
  dellablinfo,
} from "@/api/riskAdminister";
import { bcprocontractset, getlabel, commckeckvkey } from "@/api/user";
import permission from '@/store/modules/permission';

export default {
  name: "lebelAdminister",
  data() {
    // 最大杠杆
    var max_lever_V = (rules, value, callback) => {
      if (this.selectedContract.label_lever && Number(value) > Number(this.selectedContract.label_lever)) {
        return callback(new Error("该合约杠杆最高倍数为"+this.selectedContract.label_lever));
      } else {
        callback();
      }
    };
    // 标签最大下单量
    var max_order_volume_V = (rules, value, callback) => {
      if (this.selectedContract.label_max_order_volume && Number(value) > Number(this.selectedContract.label_max_order_volume)) {
        return callback(new Error("该合约最大下单量为"+this.selectedContract.label_max_order_volume+'张'));
      } else {
        callback();
      }
    };
    //单笔最小下单量
    var min_order_volume_V = (rules, value, callback) => {
      if (
        this.setLabelInfoForm.max_order_volume &&
        value &&
        Number(value) > Number(this.setLabelInfoForm.max_order_volume)
      ) {
        return callback(new Error("单笔最小下单量不能大于单笔最大下单量!!!"));
      } else {
        callback();
      }
    };
    //最大持仓量
    var max_posi_volume_V = (rules, value, callback) => {
      if (
        this.setLabelInfoForm.max_order_volume &&
        value &&
        value < Number(this.setLabelInfoForm.max_order_volume)
      ) {
        return callback(new Error("最大持仓量必须大于等于单笔最大下单量!!!"));
      } else if (this.selectedContract.label_max_posi_volume && Number(value) > Number(this.selectedContract.label_max_posi_volume)) {
        return callback(new Error("该合约最大持仓量为"+this.selectedContract.label_max_posi_volume+'张'));
      } else  {
        callback();
      }
    };
    //  最小点差
    var min_slippage_V = (rules, value, callback) => {
      let sliMin = this.selectedContract.label_slippage &&  this.selectedContract.label_slippage.split(',')[0] || ''
      let sliMax = this.selectedContract.label_slippage &&  this.selectedContract.label_slippage.split(',')[1] || ''
      let precision = sliMin.split('.')[1] && sliMin.split('.')[1].length || 2
      value = value+''
      let valPre = value && value.split('.')[1] && value.split('.')[1].length || 0
      if ((sliMin && Number(value) && Number(value) < Number(sliMin)) || (sliMax && value && Number(value) > Number(sliMax))) {
        return callback(new Error("该合约点差区间为"+sliMin+'-'+sliMax));
      }else if(valPre && precision && valPre>precision){
        return callback(new Error("该合约点差最大小数位为"+Math.pow(10,-precision)));
      }else {
        callback();
      }
    };
    //  点差
    var slippage_V = (rules, value, callback) => {
      let sliMin = this.selectedContract.label_slippage &&  this.selectedContract.label_slippage.split(',')[0] || ''
      let sliMax = this.selectedContract.label_slippage &&  this.selectedContract.label_slippage.split(',')[1] || ''
      let precision = sliMin.split('.')[1] && sliMin.split('.')[1].length || 2
      value = value+''
      let valPre = value && value.split('.')[1] && value.split('.')[1].length || 0
      if ((sliMin && Number(value) && Number(value) < Number(sliMin)) || (sliMax && value && Number(value) > Number(sliMax))) {
        return callback(new Error("该合约点差区间为"+sliMin+'-'+sliMax));
      }else if(valPre && precision && valPre>precision){
        return callback(new Error("该合约点差最大小数位为"+Math.pow(10,-precision)));
      }else if(this.setLabelInfoForm.min_slippage && Number(this.setLabelInfoForm.min_slippage)>Number(this.setLabelInfoForm.slippage)){
        return callback(new Error("最大点差应大于最小点差"));
      }  else {
        callback();
      }
    };
    //手续费率
    var fee_V = (rules, value, callback) => {
      let feeMin = this.selectedContract.label_fee &&  this.selectedContract.label_fee.split(',')[0] || ''
      let feeMax = this.selectedContract.label_fee &&  this.selectedContract.label_fee.split(',')[1] || ''
      let precision = feeMin.split('.')[1] && feeMin.split('.')[1].length || 2
      value = value+''
      let valPre = value && value.split('.')[1] && value.split('.')[1].length || 0
      if ((feeMin && Number(value) && Number(value) < Number(feeMin)) || (feeMax && value && Number(value) > Number(feeMax))) {
        return callback(new Error("该合约手续费率区间为"+feeMin+'-'+feeMax));
      }else if(valPre && precision && valPre>precision){
        return callback(new Error("该合约手续费率最大小数位为"+(Number(10).pow(-precision))));
      } else {
        callback();
      }
    };
    //  资金费率
    var funding_V = (rules, value, callback) => {
      if (this.selectedContract.label_funding && Number(value) > Number(this.selectedContract.label_funding)) {
        return callback(new Error("该合约最高资金费率为"+this.selectedContract.label_funding));
      } else {
        callback();
      }
    };
    // 最低风险率增减
    var risk_rate_V = (rules, value, callback) => {
      if (value && this.selectedContract.label_min_risk_rate && Number(value) > Number(this.selectedContract.label_min_risk_rate)) {
        return callback(new Error("该合约最低风险率为"+this.selectedContract.label_min_risk_rate));
      } else {
        callback();
      }
    };
    return {
      listLoading: false,
      total: 0,
      lebelList: null,
      listQuery: {
        pageNo: 1,
        pagesize: 10,
      },
      selectLabelForm: {
        lablname: "",
      },
      addLabelForm: {
        addLabelVal: "",
      },
      setLabelInfoForm: {
        label_id: null, //主标签 id
        contract_code: null, //合约代码
        max_lever: null, // 最大杠杆
        max_order_volume: null, // 最大下单
        min_order_volume: null, // 最小下单
        max_posi_volume: null, //  最大持仓
        fee: null, // 手续费加点
        funding: null, // 资金费用加点
        min_slippage: null,
        slippage: null, //滑点
        risk_rate: null, //风险率
      },
      ruleForm: {
        yzmVal: "", // 谷歌验证码
      },
      addLableDialog: false, // 控制google验证弹框显示
      checkvkeyDialog: false, // 控制google验证弹框显示
      addAndConfigDialogVisible: false, //控制添加标签对话框的显示和隐藏
      handleDialogType: "", //add: 添加 edit:编辑
      labelOptions: [],
      contractOptions: [],
      rules: {
        yzmVal: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        addLabelVal: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        // lablname: [
        //   {  message: "请选择标签", trigger: "change" },
        // ],
        contract_code: [
          { required: true, message: "请选择合约", trigger: "change" },
        ],
        max_lever: [{ validator: max_lever_V, trigger: "blur" }],
        max_order_volume: [{ validator: max_order_volume_V, trigger: "blur" }],
        min_order_volume: [{ validator: min_order_volume_V, trigger: "blur" }],
        max_posi_volume: [{ validator: max_posi_volume_V, trigger: "blur" }],
        min_slippage: [{ validator: min_slippage_V, trigger: "blur" }],
        slippage: [{ validator: slippage_V, trigger: "blur" }],
        fee: [{ validator: fee_V, trigger: "blur" }],
        funding: [{ validator: funding_V, trigger: "blur" }],
        risk_rate: [{ validator: risk_rate_V, trigger: "blur" }],
      },
      forms: null,
      selectedContract: {
        // 添加标签-选中合约的相关配置
        label_fee: "", //标签手续费 区间逗号隔开
        label_funding: 0, //标签最大资金费率
        label_lever: 0, //最大杠杆
        label_max_order_volume: 0, // 标签最大下单量
        label_max_posi_volume: 0, //  标签最大持仓
        label_min_risk_rate: 0, //  最低风险率
        label_slippage: "", // 标签滑点区间逗号隔开
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter((v) => v.isshow == 1);
    });
    getlabel({}).then((res) => {
      this.labelOptions = res.data;
    });
    this.getList();
  },

  methods: {
    feeInput(val){
      this.setLabelInfoForm.fee = this.clearNoNumOfAlert(val)
    },
    //只能输入数字只能有一个小数点，小数点不能在开头，不能在结尾，第一位允许添加负号
    clearNoNumOfAlert(value){
      //得到第一个字符是否为负号
      var t = value.charAt(0);  
        //先把非数字的都替换掉，除了数字和.   
        value = value.replace(/[^\d.]/g,"");   
        //必须保证第一个为数字而不是.   
        value = value.replace(/^\./g,"");   
        //保证只有出现一个.而没有多个.   
        value = value.replace(/\.{2,}/g,".");   
        //保证.只出现一次，而不能出现两次以上   
        value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        //如果第一位是负号，则允许添加
        if(t == '-'){
          value = '-'+value;
        }
        return value
    },
    contractChange(val) {
      this.selectedContract = this.contractOptions.find(
        (v) => v.traderpairs == val
      );
    },
    //点击添加标签对话框里面确定按钮
    configLabelEntry() {
      if (this.selectLabelForm.lablname) {
        this.$refs["selectLabelForm"].validate((valid) => {
          if (valid) {
            this.$refs["setLabelInfoForm"].validate((valid) => {
              if (valid) {
                this.checkvkeyDialog = true;
              }
            });
          }
        });
      } else {
        this.$refs["selectLabelForm"].validate((valid) => {
          if (valid) {
          }
        });
      }
    },
    //点击添加标签出现弹框
    AddClick() {
      this.handleDialogType = "add";
      this.addAndConfigDialogVisible = true;
      this.$nextTick(() => {
        this.$refs["setLabelInfoForm"].clearValidate();
      });
    },
    // 列表删除按钮
    handleDel(row) {
      this.$confirm("是否确认删除此标签?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          dellablinfo({ id: row.id }).then((res) => {
            this.$notify({
              title: "操作",
              message: "删除成功",
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        })
        .catch(() => {});
    },
    // 列表编辑按钮
    handleEdit(row) {
      this.selectLabelForm.lablname = row.label_id;
      Object.assign(this.setLabelInfoForm, row);
      this.addAndConfigDialogVisible = true;
      this.handleDialogType = "edit";
      this.contractChange(row.contract_code);
    },
    //状态开启和关闭
    handleModifyStatus(row) {
      this.$confirm(`确认${row.status_stype ? "关闭" : "开启"}？`)
        .then((_) => {
          let data = JSON.parse(JSON.stringify(row));
          // for (const key in data) {
          //   if (data.hasOwnProperty(key)) {
          //     const element = data[key];
          //     if(/^[0-9]+.?[0-9]*$/.test(element)){
          //       data[key] = Number(element)
          //       return false
          //     }
          //   }
          // }
          Object.assign(data, { status_stype: data.status_stype ? 0 : 1 });
          updatelablinfo(data).then((res) => {
            this.$notify({
              title: "操作",
              message: "操作成功",
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        })
        .catch((_) => {});
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      getlablinfo(this.listQuery).then((response) => {
        this.lebelList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    closeConfigLabelDialog() {
      this.addLabelForm.addLabelVal = "";
      (this.setLabelInfoForm = {
        label_id: null, //主标签 id
        contract_code: null, //合约代码
        max_lever: null, // 最大杠杆
        max_order_volume: null, // 最大下单
        min_order_volume: null, // 最小下单
        max_posi_volume: null, //  最大持仓
        fee: null, // 手续费加点
        funding: null, // 资金费用加点
        min_slippage: null,
        slippage: null, //滑点
        risk_rate: null,
      }),
        this.$refs["selectLabelForm"].resetFields();
      this.$refs["setLabelInfoForm"].resetFields();
    },
    addLabelResetFields() {
      this.$refs["addLabelForm"].resetFields();
    },
    entryAddLabel() {
      this.$refs["addLabelForm"].validate((valid) => {
        if (valid) {
          addlabel({
            lablname: this.addLabelForm.addLabelVal,
          }).then((res) => {
            this.addLableDialog = false;
            this.addLabelForm.addLabelVal = "";
            this.$notify({
              title: "成功",
              message: "操作成功",
              type: "success",
              duration: 2000,
            });
            getlabel({}).then((res) => {
              this.labelOptions = res.data;
            });
          });
        } else {
          return false;
        }
      });
    },
    closexgmm() {
      this.$refs["ruleForm"].resetFields();
    },
    entrySendyzm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          commckeckvkey({ code: this.ruleForm.yzmVal }).then((res) => {
            let data = this.setLabelInfoForm;
            // 对象内字段value转换成Number
            for (const key in data) {
              if (data.hasOwnProperty(key)) {
                const element = data[key];
                // 排除非转换Number的
                if (['contract_code','creat_time','label_name',].indexOf(key) == -1) {
                  data[key] = Number(element) || 0;
                } else {
                  if (!element) {
                    data[key] = 0;
                  }
                }
              }
            }
            Object.assign(data, { label_id: this.selectLabelForm.lablname });
            if (this.handleDialogType == "add") {
              addlablinfo(data).then((res) => {
                this.ruleForm.yzmVal = "";
                this.closeConfigLabelDialog();
                this.checkvkeyDialog = false;
                this.addAndConfigDialogVisible = false;
                this.$notify({
                  title: "添加",
                  message: "操作成功",
                  type: "success",
                  duration: 2000,
                });
                this.getList();
              });
            } else {
              updatelablinfo(data).then((res) => {
                this.ruleForm.yzmVal = "";
                this.closeConfigLabelDialog();
                this.checkvkeyDialog = false;
                this.addAndConfigDialogVisible = false;
                this.$notify({
                  title: "修改",
                  message: "操作成功",
                  type: "success",
                  duration: 2000,
                });
                this.getList();
              });
            }
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
</style>