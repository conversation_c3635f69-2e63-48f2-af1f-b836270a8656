import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)


/* Layout */
import Layout from '@/layout'
/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [{
    path: '/login',
    component: () =>
        import('@/views/login/index'),
    hidden: true
},
{
    path: '/404',
    component: () =>
        import('@/views/404'),
    hidden: true
},

{
    path: '/',
    component: Layout,
    redirect: '/home',
    hidden: true,
    children: [{
        path: 'home',
        name: 'welcomeHome',
        component: () =>
            import('@/views/home/<USER>'),
        meta: { title: '平台管理系统', icon: 'dashboard' }
    }]
},
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
    {
        path: '/notice',
        component: Layout,
        redirect: '/notice/gglb',
        name: 'notice',
        meta: { title: '公告管理', icon: 'el-icon-s-platform',  roles: ['gglb','ggfb','getnoticeimportant'] },
        // roles: ['notice']
        children: [{
            path: 'gglb',
            name: 'gglb-noKeep',
            component: () => import('@/views/notice/gglb'),
            meta: { title: '公告列表', icon: 'el-icon-s-platform', roles: ['gglb'] }
        },
        {
            path: 'ggfb',
            name: 'ggfb',
            component: () => import('@/views/notice/ggfb'),
            meta: { title: '公告发布', icon: 'el-icon-s-platform', roles: ['ggfb'] },
        },
        {
            path: 'ggxg',
            name: 'ggxg',
            component: () => import('@/views/notice/ggxg'),
            meta: { title: '公告修改', icon: 'el-icon-s-platform', roles: ['ggxg'] },
            hidden: true
        },
        {
            path: 'importantNotice',
            name: 'importantNotice',
            component: () => import('@/views/notice/importantNotice'),
            meta: { title: '首页浮层', icon: 'el-icon-s-platform', roles: ['getnoticeimportant'] },
        }
        ]
    },
    {
        path: '/user',
        component: Layout,
        redirect: '/user/list',
        name: 'username',
        meta: { title: '用户查询', icon: 'el-icon-user', roles: ['list','verifyhistorylist'] },
        children: [{
            path: 'list',
            name: 'userList',
            component: () => import('@/views/userQuery/userList/index'),
            meta: { title: '用户列表', icon: 'el-icon-user', roles: ['list'] }
        },
        {
            path: 'detail',
            name: 'userListDetail-noKeep',
            component: () => import('@/views/userQuery/userListDetail'),
            meta: { title: '用户信息详情', icon: 'el-icon-user', roles: ['list'] },
            hidden: true
        },
        {
            path: 'kyc',
            name: 'Kyc',
            component: () => import('@/views/userQuery/kyc/index'),
            meta: { title: 'KYC审核', icon: 'el-icon-user', roles: ['verifyhistorylist'] }
        }
        ]
    },
    {
        path: '/transactionQuery',
        component: Layout,
        redirect: '/transactionQuery/holdquery',
        name: 'trade',
        meta: { title: '交易查询', icon: 'el-icon-s-order', roles: ['positionlist', 'closetradelist', 'opentradelist','tradelist', 'plancloseorder','getplanorder','getposstioncap','getmanageexpotlist'] },
        children: [{
            path: 'holdquery',
            name: 'holdquery',
            component: () => import('@/views/transactionQuery/holdQuery/index'),
            meta: { title: '持仓查询', icon: 'el-icon-s-order', roles: ['positionlist'] }
        },
        {
            path: 'levelquery',
            name: 'levelquery',
            component: () => import('@/views/transactionQuery/levelQuery/index'),
            meta: { title: '平仓查询', icon: 'el-icon-s-order', roles: ['closetradelist'] }
        },
        {
            path: 'openquery',
            name: 'openquery',
            component: () => import('@/views/transactionQuery/openQuery/index'),
            meta: { title: '开仓查询', icon: 'el-icon-s-order', roles: ['opentradelist'] }
        },
        {
            path: 'tradelist',
            name: 'tradelist',
            component: () => import('@/views/transactionQuery/tradeList/index'),
            meta: { title: '开平仓查询', icon: 'el-icon-s-order', roles: ['tradelist'] }
        },
        {
            path: 'PLQuery',
            name: 'PLQuery',
            component: () => import('@/views/transactionQuery/PLQuery/index'),
            meta: { title: '止盈止损查询', icon: 'el-icon-s-order', roles: ['plancloseorder'] }
        },
        {
            path: 'planQuery',
            name: 'planQuery',
            component: () => import('@/views/transactionQuery/planQuery/index'),
            meta: { title: '计划单查询', icon: 'el-icon-s-order', roles: ['getplanorder'] }
        },
        {
            path: 'posstioncap',
            name: 'posstioncap',
            component: () => import('@/views/transactionQuery/posstioncap/index'),
            meta: { title: '用户持仓监控', icon: 'el-icon-s-order', roles: ['getposstioncap'] }
        },
        {
            path: 'exportList',
            name: 'exportList',
            component: () => import('@/views/transactionQuery/exportList/index'),
            meta: { title: '导出下载列表', icon: 'el-icon-s-order', roles: ['getmanageexpotlist'] }
        },
        ]
    },
    {
        path: '/followQuery',
        component: Layout,
        redirect: '/followQuery/holdquery',
        name: 'followQuery',
        meta: { title: '跟单查询', icon: 'el-icon-document-copy', roles: ['docpositionlist', 'docclosetradelist', 'docopentradelist','followtradelist','docplancloseorder','docgetplanorder', 'followaccinfo', 'getflposstioncap', 'getfollowcapital'] },
        children: [{
            path: 'holdquery',
            name: 'followholdquery',
            component: () => import('@/views/documentaryQuery/holdQuery/index'),
            meta: { title: '持仓查询', icon: 'el-icon-document-copy', roles: ['docpositionlist'] }
        },
        {
            path: 'levelquery',
            name: 'followlevelquery',
            component: () => import('@/views/documentaryQuery/levelQuery/index'),
            meta: { title: '平仓查询', icon: 'el-icon-document-copy', roles: ['docclosetradelist'] }
        },
        {
            path: 'openquery',
            name: 'followopenquery',
            component: () => import('@/views/documentaryQuery/openQuery/index'),
            meta: { title: '开仓查询', icon: 'el-icon-document-copy', roles: ['docopentradelist'] }
        },
        {
            path: 'followtradelist',
            name: 'followtradelist',
            component: () => import('@/views/documentaryQuery/followtradelist/index'),
            meta: { title: '开平仓查询', icon: 'el-icon-document-copy', roles: ['followtradelist'] }
        },
        {
            path: 'PLQuery',
            name: 'followPLQuery',
            component: () => import('@/views/documentaryQuery/PLQuery/index'),
            meta: { title: '止盈止损查询', icon: 'el-icon-document-copy', roles: ['docplancloseorder'] }
        },
        {
            path: 'planQuery',
            name: 'followplanQuery',
            component: () => import('@/views/documentaryQuery/planQuery/index'),
            meta: { title: '计划委托查询', icon: 'el-icon-document-copy', roles: ['docgetplanorder'] }
        },
        {
            path: 'followaccinfo',
            name: 'followaccinfo',
            component: () => import('@/views/documentaryQuery/followaccinfo/index'),
            meta: { title: '跟单资产查询', icon: 'el-icon-document-copy', roles: ['followaccinfo'] }
        },
        {
            path: 'flposstioncap',
            name: 'flposstioncap',
            component: () => import('@/views/documentaryQuery/flposstioncap/index'),
            meta: { title: '跟单持仓监控', icon: 'el-icon-document-copy', roles: ['getflposstioncap'] }
        },
        {
            path: 'followcapital',
            name: 'followcapital',
            component: () => import('@/views/documentaryQuery/followcapital/index'),
            meta: { title: '跟单数据监控', icon: 'el-icon-document-copy', roles: ['getfollowcapital'] }
        },
        ]
    },
    {
        path: '/fundQuery',
        component: Layout,
        redirect: '/fundQuery/userAssetQuery',
        name: 'fundQuery',
        meta: { title: '资金查询', icon: 'el-icon-tickets', roles: ['getwellinfo', 'getaccinfo', 'getwalletbill', 'getwallerhistory', 'getwithdrawlist','legalorderlist','getagentcapital', 'getuserpnl', 'getusercaption', 'getcommiss', 'daypnllist'] },
        children: [{
            path: 'userAssetQuery',
            name: 'userAssetQuery',
            component: () => import('@/views/fundQuery/userAssetQuery/index'),
            meta: { title: '用户资产查询', icon: 'el-icon-tickets', roles: ['getwellinfo'] }
        },{
            path: 'tradeAssetQuery',
            name: 'tradeAssetQuery',
            component: () => import('@/views/fundQuery/tradeAssetQuery/index'),
            meta: { title: '交易资产查询', icon: 'el-icon-tickets', roles: ['getaccinfo'] }
        },
        {
            path: 'goldquery',
            name: 'goldquery',
            component: () => import('@/views/fundQuery/goldquery/index'),
            meta: { title: '用户出入金查询', icon: 'el-icon-tickets', roles: ['getwalletbill'] }
        },
        {
            path: 'financialRecords',
            name: 'financialrecords',
            component: () => import('@/views/fundQuery/financialRecords/index'),
            meta: { title: '用户财务记录', icon: 'el-icon-tickets', roles: ['getwallerhistory'] }
        },
        {
            path: 'withdrawalAdminister',
            name: 'withdrawaladminister',
            component: () => import('@/views/fundQuery/withdrawalAdminister/index'),
            meta: { title: '用户提币管理', icon: 'el-icon-tickets', roles: ['getwithdrawlist'] }
        },
        {
            path: 'agentCapital',
            name: 'agentCapital',
            component: () => import('@/views/fundQuery/agentCapital/index'),
            meta: { title: '用户数据监控', icon: 'el-icon-tickets', roles: ['getagentcapital'] }
        },
        {
            path: 'legalOrder',
            name: 'legalOrder',
            component: () => import('@/views/fundQuery/legalOrder/index'),
            meta: { title: '法币卖出管理', icon: 'el-icon-tickets', roles: ['legalorderlist'] }
        },
        {
            path: 'pnlQuery',
            name: 'pnlQuery',
            component: () => import('@/views/fundQuery/pnlQuery/index'),
            meta: { title: 'PNL查询', icon: 'el-icon-tickets', roles: ['getuserpnl'] }
        },
        {
            path: 'usercaption',
            name: 'usercaption',
            component: () => import('@/views/fundQuery/usercaption/index'),
            meta: { title: '资金费用查询', icon: 'el-icon-tickets', roles: ['getusercaption'] }
        },
        {
            path: 'commiss',
            name: 'commiss',
            component: () => import('@/views/fundQuery/commiss/index'),
            meta: { title: '手续费查询', icon: 'el-icon-tickets', roles: ['getcommiss'] }
        },
        {
            path: 'daypnllist',
            name: 'daypnllist',
            component: () => import('@/views/fundQuery/daypnllist/index'),
            meta: { title: '每日PNL汇总', icon: 'el-icon-tickets', roles: ['daypnllist'] }
        },
        ]
    },
    {
        path: '/rebateQuery',
        component: Layout,
        redirect: '/rebateQuery/rebateList',
        name: 'rebateList',
        meta: { title: '返佣查询', icon: 'el-icon-coin', roles: ['getrebotlist', 'getuserrebot'] },
        alwaysShow: true,
        children: [{
            path: 'rebateList',
            name: 'rebatelist',
            component: () => import('@/views/rebateQuery/rebateList/index'),
            meta: { title: '手续费返佣', icon: 'el-icon-coin', roles: ['getrebotlist'] },
        },
        {
            path: 'rebateListDetail',
            name: 'rebateListDetail-noKeep',
            component: () => import('@/views/rebateQuery/rebateListDetail/index'),
            meta: { title: '手续费返佣', icon: 'el-icon-coin', roles: ['getuserrebot'] },
            hidden: true
        },
        ]
    },
    {
        path: '/platformFinance/assetAccount',
        component: Layout,
        redirect: '/platformFinance/assetAccount',
        name: 'platformfinance',
        meta: { title: '平台财务', icon: 'el-icon-bank-card', roles: ['platwalletlist','getplataccount'] }, // 'getfinacewallet',
        children: [
        // {
        //     path: 'overView',
        //     name: 'overView',
        //     component: () => import('@/views/platformFinance/overView/index'),
        //     meta: { title: '概览', icon: 'el-icon-bank-card', roles: ['getfinacewallet'] },
        // },
        {
            path: 'assetAccount',
            name: 'assetAccount',
            component: () => import('@/views/platformFinance/assetAccount/index'),
            meta: { title: '资产账户', icon: 'el-icon-bank-card', roles: ['platwalletlist'] },
        },
        {
            path: 'assetHistoryRecord',
            name: 'assetHistoryRecord',
            component: () => import('@/views/platformFinance/assetAccount/assetHistoryRecord'),
            meta: { title: '历史记录', icon: 'el-icon-bank-card', roles: ['platwalletdaillist'] },
            hidden: true
        },
        // {
        //     path: 'transactionAccount',
        //     name: 'transactionAccount',
        //     component: () => import('@/views/platformFinance/transactionAccount/index'),
        //     meta: { title: '交易账户', icon: 'el-icon-bank-card', roles: ['getplataccount'] },
        // },
        {
            path: 'transactionHistoryRecord',
            name: 'transactionHistoryRecord',
            component: () => import('@/views/platformFinance/transactionAccount/transactionHistoryRecord'),
            meta: { title: '历史记录', icon: 'el-icon-bank-card', roles: ['plataccountdail'] },
            hidden: true
        },
        ]
    },
    {
        path: '/activity/',
        component: Layout,
        redirect: '/activity/shareHandlingFee',
        name: 'activity',
        meta: { title: '活动管理', icon: 'el-icon-timer', roles: ['bcactivitylist'] },
        alwaysShow: true,
        children: [
            {
                path: 'shareHandlingFee',
                name: 'shareHandlingFee',
                component: () => import('@/views/activity/shareHandlingFee/index'),
                meta: { title: '瓜分手续费', icon: 'el-icon-timer', roles: ['bcactivitylist'] },
            },
            {
                path: 'feeDetails',
                name: 'feeDetails-noKeep',
                component: () => import('@/views/activity/shareHandlingFee/details'),
                meta: { title: '瓜分手续费详情', icon: 'el-icon-coin', roles: ['getuseractivitylist'] },
                hidden: true
            },
        ]
    },
    {
        path: '/channelManage',
        component: Layout,
        redirect: '/channelManage/topAgentList',
        name: 'channelManage',
        meta: { title: '渠道管理', icon: 'el-icon-coin', roles: ['topAgentList', 'agentDirectlist'] },
        alwaysShow: true,
        children: [{
            path: 'topAgentList',
            name: 'topAgentList',
            component: () => import('@/views/channelManage/topAgentList/index'),
            meta: { title: '顶级代理统计', icon: 'el-icon-coin', roles: ['topAgentList'] },
        },{
            path: 'agentDirectlist',
            name: 'agentDirectlist',
            component: () => import('@/views/channelManage/agentDirectlist/index'),
            meta: { title: '代理直推统计', icon: 'el-icon-coin', roles: ['agentDirectlist'] },
        },
        ]
    },
    {
        path: '/riskAdminister',
        component: Layout,
        redirect: '/riskAdminister/lebelAdminister',
        name: 'windcontrol',
        meta: { title: '风控管理', icon: 'el-icon-chat-dot-square', roles: ['bcprocontractcoinset','frequency', 'highfrequency', 'highpnl', 'highwinning', 'systemmonitoring','ipstatisticslist','ipuserlist','iptradelist','watchuserlist', 'getgrouplist', 'getriskuserlist',] },
        alwaysShow: true,
        children: [
            {
                path: 'lebelAdminister',
                name: 'lebelAdminister',
                component: () => import('@/views/riskAdminister/lebelAdminister/index'),
                meta: { title: '标签管理', icon: 'el-icon-chat-dot-square', roles: ['bcprocontractcoinset'] },
            },
            {
                path: 'frequency',
                name: 'frequency',
                component: () => import('@/views/riskAdminister/frequency/index'),
                meta: { title: '交易频次', icon: 'el-icon-chat-dot-square', roles: ['frequency'] },
            },
            {
                path: 'frequencyDetail',
                name: 'frequencyDetail-noKeep',
                component: () => import('@/views/riskAdminister/frequencyDetail/index'),
                meta: { title: '交易频次--详情', icon: 'el-icon-chat-dot-square',roles: ['bcprocontractcoinset'] },
                hidden: true
            },
            {
                path: 'highfrequency',
                name: 'highfrequency',
                component: () => import('@/views/riskAdminister/highfrequency/index'),
                meta: { title: '高频用户识别', icon: 'el-icon-chat-dot-square',roles: ['highfrequency'] },
            },
            {
                path: 'highpnl',
                name: 'highpnl',
                component: () => import('@/views/riskAdminister/highpnl/index'),
                meta: { title: '高盈利用户识别', icon: 'el-icon-chat-dot-square',roles: ['highpnl'] },
            },
            {
                path: 'highwinning',
                name: 'highwinning',
                component: () => import('@/views/riskAdminister/highwinning/index'),
                meta: { title: '高胜率用户识别', icon: 'el-icon-chat-dot-square',roles: ['highwinning'] },
            },
            {
                path: 'highwinningdetails',
                name: 'highwinningdetails-noKeep',
                component: () => import('@/views/riskAdminister/highwinning/highwinningdetails'),
                meta: { title: '高胜率用户信息详情', icon: 'el-icon-chat-dot-square', roles: ['highwinning'] },
                hidden: true
            },
            {
                path: 'systemmonitoring',
                name: 'systemmonitoring',
                component: () => import('@/views/riskAdminister/systemmonitoring/index'),
                meta: { title: '系统监控', icon: 'el-icon-chat-dot-square',roles: ['systemmonitoring'] },
            },
            {
                path: 'ipstatisticslist',
                name: 'ipstatisticslist',
                component: () => import('@/views/riskAdminister/ipstatisticslist/index'),
                meta: { title: '同IP行为分析', icon: 'el-icon-chat-dot-square',roles: ['ipstatisticslist'] },
            },
            {
                path: 'ipuserlist',
                name: 'ipuserlist',
                component: () => import('@/views/riskAdminister/ipstatisticslist/ipuserlist'),
                meta: { title: 'IP用户概况', icon: 'el-icon-chat-dot-square',roles: ['ipuserlist'] },
                hidden: true
            },
            {
                path: 'iptradelist',
                name: 'iptradelist',
                component: () => import('@/views/riskAdminister/ipstatisticslist/iptradelist'),
                meta: { title: 'IP交易详情', icon: 'el-icon-chat-dot-square',roles: ['iptradelist'] },
                hidden: true
            },
            {
                path: 'watchuserlist',
                name: 'watchuserlist',
                component: () => import('@/views/riskAdminister/watchuserlist/index'),
                meta: { title: '观察用户列表', icon: 'el-icon-chat-dot-square',roles: ['watchuserlist'] },
            },
            {
                path: 'riskgroup',
                name: 'riskgroup',
                component: () => import('@/views/riskAdminister/riskgroup/index'),
                meta: { title: '风控组别列表', icon: 'el-icon-chat-dot-square',roles: ['getgrouplist'] },
            },
            {
                path: 'riskuserlist',
                name: 'riskuserlist',
                component: () => import('@/views/riskAdminister/riskuserlist/index'),
                meta: { title: '风控用户列表', icon: 'el-icon-chat-dot-square',roles: ['getriskuserlist'] },
            },
            {
                path: 'riskuserwhite',
                name: 'riskuserwhite',
                component: () => import('@/views/riskAdminister/riskuserlist/riskuserwhite'),
                meta: { title: '风控用户白名单', icon: 'el-icon-chat-dot-square',roles: ['getwhitelist'] },
                hidden: true
            },
            {
                path: 'riskuserhistory',
                name: 'riskuserhistory',
                component: () => import('@/views/riskAdminister/riskuserlist/riskuserhistory'),
                meta: { title: '风控用户历史记录', icon: 'el-icon-chat-dot-square',roles: ['getristlist'] },
                hidden: true
            },

        ]
    },
    {
        path: '/systemAdminister',
        component: Layout,
        redirect: '/systemAdminister',
        name: 'systemmanage',
        meta: { title: '系统管理', icon: 'el-icon-s-tools', roles: ['bannerConfig','managelist','moldelist','getmanagelogs','contactUs','userlog','usererrlog', 'getagentoplog'] },
        children: [
            {
                path: 'bannerConfig',
                name: 'bannerConfig',
                component: () => import('@/views/systemAdminister/bannerConfig'),
                meta: { title: '首页Banner配置', icon: 'el-icon-s-tools', roles: ['bannerConfig'] },
            },
            {
                path: 'roleAdminister',
                name: 'roleAdminister',
                component: () => import('@/views/systemAdminister/roleAdminister/index'),
                meta: { title: '角色管理', icon: 'el-icon-s-tools', roles: ['managelist'] },
            },
            {
                path: 'authorityGrouping',
                name: 'authorityGrouping',
                component: () => import('@/views/systemAdminister/authorityGrouping/index'),
                meta: { title: '权限分组', icon: 'el-icon-s-tools', roles: ['moldelist'] },
            },
            {
                path: 'addGroup',
                name: 'addGroup-noKeep',
                component: () => import('@/views/systemAdminister/authorityGrouping/addGroup/index'),
                meta: { title: '添加分组', icon: 'el-icon-s-tools', roles: ['moldeladd1'] },
                hidden: true
            },
            {
                path: 'operatingLog',
                name: 'operatingLog',
                component: () => import('@/views/systemAdminister/operatingLog/index'),
                meta: { title: '操作日志', icon: 'el-icon-s-tools', roles: ['getmanagelogs'] },
            },
            {
                path: 'getagentoplog',
                name: 'getagentoplog',
                component: () => import('@/views/systemAdminister/getagentoplog/index'),
                meta: { title: 'CRM操作日志', icon: 'el-icon-s-tools', roles: ['getagentoplog'] },
            },
            {
                path: 'contactUs',
                name: 'contactUs',
                component: () => import('@/views/systemAdminister/contactUs/index'),
                meta: { title: '联系我们', icon: 'el-icon-s-tools', roles: ['contactUs'] },
            },
            {
                path: 'userlog',
                name: 'userlog',
                component: () => import('@/views/systemAdminister/userLog/index'),
                meta: { title: '用户日志', icon: 'el-icon-s-tools', roles: ['userlog'] },
            },
            {
                path: 'usererrlog',
                name: 'usererrlog',
                component: () => import('@/views/systemAdminister/usererrlog/index'),
                meta: { title: '反馈数据', icon: 'el-icon-s-tools', roles: ['usererrlog'] },
            },
            
            {
                path: 'securityCenter',
                name: 'securityCenter',
                component: () => import('@/views/systemAdminister/securityCenter/index'),
                meta: { title: '安全中心', icon: 'el-icon-s-tools', roles: ['aaaaaaa'] },
            },
            {
                path: 'changePassword',
                name: 'changePassword',
                component: () => import('@/views/systemAdminister/securityCenter/changePassword/index'),
                meta: { title: '修改密码', icon: 'el-icon-s-tools', roles: ['aaaaaaa'] },
                hidden: true
            },
            {
                path: 'changeGoogle',
                name: 'changeGoogle',
                component: () => import('@/views/systemAdminister/securityCenter/changeGoogle/index'),
                meta: { title: '修改谷歌验证器', icon: 'el-icon-s-tools', roles: ['aaaaaaa'] },
                hidden: true
            },

        ]
    },
    { path: '*', redirect: '/', hidden: true }
]

const createRouter = () => new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
    const newRouter = createRouter()
    router.matcher = newRouter.matcher // reset router
}

export default router