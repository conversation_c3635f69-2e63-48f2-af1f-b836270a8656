 <template>
  <div class="rebatelistdetail_wrap">
    <div class="parList_wrap">
      <table style="table-layout: fixed; border-collapse: collapse">
        <tr>
          <th>活动日期</th>
          <th>活动名称</th>
          <th>活动描述</th>
          <th>奖池最高上限</th>
          <th>注入手续费比例</th>
          <th>开始日期</th>
          <th>结束日期</th>
          <th>每期开始时间</th>
          <th>每期开奖时间</th>
          <th>每期结束时间</th>
          <th>活动状态</th>
          <th>已发奖励</th>
        </tr>

        <tr>
          <td>{{ (rebateList && rebateList.own_day) || "--" }}</td>
          <td>{{ rebateList.activity_name || "--" }}</td>
          <td>{{ rebateList.describe || "--" }}</td>
          <td>{{ rebateList.limit_up || "--" }}</td>
          <td>{{ rebateList.ratio }}</td>
          <td>{{ rebateList.start_day }}</td>
          <td>{{ rebateList.end_day }}</td>
          <td>
            <span
              v-html="
                rebateList.start_time
                  ? rebateList.start_time.split(' ')[0] +
                    '<br/>' +
                    rebateList.start_time.split(' ')[1]
                  : '--'
              "
            ></span>
          </td>
          <td>
            <span
              v-html="
                rebateList.opening_time
                  ? rebateList.opening_time.split(' ')[0] +
                    '<br/>' +
                    rebateList.opening_time.split(' ')[1]
                  : '--'
              "
            ></span>
          </td>
          <td>
            <span
              v-html="
                rebateList.end_time
                  ? rebateList.end_time.split(' ')[0] +
                    '<br/>' +
                    rebateList.end_time.split(' ')[1]
                  : '--'
              "
            ></span>
          </td>
          <td>
            <span>{{ rebateList.ac_status ? "进行中" : "已结束" }}</span>
          </td>
          <td>{{ rebateList.lssued }}</td>
        </tr>
      </table>
    </div>

    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-select
        size="mini"
        v-model="listQuery.grade"
        placeholder="状态"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="(item, index) in gradeOptionsArr"
          :key="index"
          :label="item.name"
          :value="item.key"
        />
      </el-select>

      <el-select
        size="mini"
        v-model="listQuery.check_status"
        placeholder="状态"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="(item, index) in stateOptions"
          :key="index"
          :label="item.name"
          :value="item.key"
        />
      </el-select>

      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-left: 20px"
        class="picker"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change="filterTimeTransform"
      >
      </el-date-picker>
      <el-button
        class="filter-item"
        style="margin-left: 10px"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px"
        size="mini"
        type="primary"
        :disabled="!multipleSelection.length"
        @click="oneClickSendrebot"
      >
        一键通过
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="rebateDetailList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
      @selection-change="handleSelectionChange">
    >
      <el-table-column :selectable="checkSelect" label-class-name="DisabledSelection" align="center" type="selection" width="70px"></el-table-column>
      <el-table-column label="UID" prop="uid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="用户名"
        prop="user_name"
        align="center"
        min-width="90px"
      />
      <el-table-column
        label="交易时间"
        prop="open_time"
        align="center"
        min-width="95px"
      >
        <template slot-scope="{ row }">
          <span
            v-html="
              row.open_time
                ? row.open_time.split(' ')[0] +
                  '<br/>' +
                  row.open_time.split(' ')[1]
                : '--'
            "
          ></span>
        </template>
      </el-table-column>
      <el-table-column
        label="交易编号"
        prop="order_id"
        align="center"
        min-width="78"
      />
      <el-table-column
        label="奖励类型"
        prop="order_id"
        align="center"
        min-width="95px"
      >
        <template slot-scope="{ row }">
          <span>{{ gradeOptions[row.grade] || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="金额"
        prop="bonus"
        align="center"
        min-width="90px"
      />
      <el-table-column
        label="上传照片"
        prop="share_img"
        align="center"
        min-width="100px"
      >
        <template slot-scope="{ row }">
          <div>
            <el-image
              v-if="row.share_img"
              style="width: 50px"
              :src="row.share_img"
              :preview-src-list="[row.share_img]"
            >
            </el-image>
            <span v-else>--</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        prop="check_status"
        align="center"
        min-width="100px"
      >
        <template slot-scope="{ row }">
          <span>{{ statusOption[row.check_status] || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="审核时间"
        prop="check_time"
        align="center"
        min-width="90px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.check_time == "null" ? "--" : row.check_time }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" v-if="$store.getters.roles.indexOf('useractivitycheck') > -1" align="center" min-width="150px">
        <template slot-scope="{ row }">
          <el-button
            :disabled="row.check_status != 0"
            type="primary"
            size="mini"
            @click="handleBy(row, 1)"
            >通过</el-button
          >
          <el-button
            :disabled="row.check_status != 0"
            type="warning"
            size="mini"
            @click="handleBy(row, 2)"
            >驳回</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[100,200,300,500]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="rebatelistDetail"
    />
  </div>
</template>

<script>
import { getuseractivitylist, useractivitycheck } from "@/api/activity";

export default {
  name: "feeDetails",
  data() {
    return {
      listLoading: false,

      rebateDetailList: null, //接受详情数据

      rebateList: {
        activity_name: "",
        current_bonus: 0,
        describe: "",
        end_day: "",
        end_time: "",
        first_prize: 0,
        hash: "",
        id: 0,
        limit_up: "",
        lssued: "",
        opening_time: "",
        own_day: "",
        ratio: 0,
        second_award: 0,
        set_bonus: 0,
        start_day: "",
        start_time: "",
        third_award: 0,
      }, //详情带进来的数据
      filterTime: [],
      gradeOptionsArr: [
        { key: 1, name: "一等奖" },
        { key: 2, name: "二等奖" },
        { key: 3, name: "三等奖" },
      ],
      stateOptions: [
        { key: -1, name: "待上传图片" },
        { key: 0, name: "待审核" },
        { key: 1, name: "审核通过" },
        { key: 2, name: "已拒绝" },
        { key: 3, name: "已过期" },
      ],
      gradeOptions: {
        1: "一等奖",
        2: "二等奖",
        3: "三等奖",
      },
      statusOption: {
        "-1": "待上传图片",
        0: "待审核",
        1: "审核通过",
        2: "已拒绝",
        3: "已过期",
      },
      listQuery: {
        sname: "", // 用户
        uid: "", // 名字或者手机号，
        grade: undefined, // 奖励类型 1一等奖 2二等奖 3 三等奖，
        check_status: undefined, // 状态  -1待领取，0待审核，1审核通过 2拒绝，3过期 ，
        start: "", // ，
        end: "", // ，
        pageNo: 1,
        pagesize: 100,
      },
      total: 0,
      multipleSelection: [],
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.rebateList = JSON.parse(this.$route.query.dt);
    this.rebatelistDetail();
  },

  methods: {
    // 一键通过
    oneClickSendrebot(){
      if(this.multipleSelection.length){
        this.$confirm('确认一键通过？')
        .then(_ => {
          this.multipleSelection.forEach((v,i)=>{
            useractivitycheck({ id: v.id, ctype: 1 }).then((res) => {
              if(this.multipleSelection.length-1 === i){
                this.$notify({
                  title:'操作',
                  message: '操作成功',
                  type: "success",
                  duration: 2000,
                })
                this.rebatelistDetail();
              }
            });
          })
        })
        .catch(_ => {});
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    checkSelect (row,index) {
      let isChecked = true;
      if (row.check_status === 0) { // 判断里面是否存在某个参数
        isChecked = true
      } else {
        isChecked = false
      }
      return isChecked
    },
    // 通过 点击事件
    handleBy(row, type) {
      this.$confirm(`是否确认审核${type == 1 ? "通过" : "驳回"}?`, "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: type == 1 ? "success" : "warning",
      })
        .then(() => {
          useractivitycheck({ id: row.id, ctype: type }).then((res) => {
            this.$notify({
              title: "成功",
              message: "操作成功",
              type: "success",
              duration: 2000,
            });
            this.rebatelistDetail();
          });
        })
        .catch(() => {});
    },
    rebatelistDetail() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, {
        grade: this.listQuery.grade || 0,
        check_status:
          this.listQuery.check_status !== 0
            ? this.listQuery.check_status || -2
            : 0,
        acid: this.rebateList.id
      });
      getuseractivitylist(data).then((res) => {
        this.rebateDetailList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.rebatelistDetail();
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = (val && val[1] + " 23:59:59") || "";
    },
  },
};
</script>
<style lang="scss">
.rebatelistdetail_wrap{
  /*表格表头全选*/
	.el-table .DisabledSelection .cell .el-checkbox__inner{
	  margin-left: -30px;
	  position:relative;
	}
	.el-table .DisabledSelection .cell:before{
	  content:"全选";
	  position:absolute;
	  right:11px;
	}
}

</style>
<style lang="scss" scoped>
.rebatelistdetail_wrap {
  .parList_wrap {
    table {
      width: 95%;
      border: 1px solid #c0c4cc;
      border-collapse: collapse;
      margin-bottom: 15px;
      font-size: 14px;
      margin: 20px auto;
      background: #f7f9fc;
      tr {
        width: 100%;
        text-align: center;
        height: 40px;
        th {
          width: 10%;
          border: 1px solid #c0c4cc;
          font-size: 14px;
          height: 40px;
          white-space: nowrap; /* 自适应宽度*/
          word-break: keep-all; /* 避免长单词截断，保持全部 */
          white-space: pre-line;
          word-break: break-all !important;
          word-wrap: break-word !important;
          display: table-cell;
          vertical-align: middle !important;
          white-space: normal !important;
          vertical-align: text-top;
          padding: 2px 2px 0 2px;
        }
        td {
          width: 10%;
          border: 1px solid #c0c4cc;
          white-space: nowrap; /* 自适应宽度*/
          word-break: keep-all; /* 避免长单词截断，保持全部 */
          white-space: pre-line;
          word-break: break-all !important;
          word-wrap: break-word !important;
          display: table-cell;
          vertical-align: middle !important;
          white-space: normal !important;
          vertical-align: text-top;
          padding: 2px 2px 0 2px;
          display: table-cell;
        }
      }
    }
  }
}
</style>