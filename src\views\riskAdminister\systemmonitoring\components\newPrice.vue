<template>
  <ul class="contractMarket_wrap">
    <li>{{tableData.code}}</li>
    <li>{{tableData.buy || '--'}}</li>
    <li :style="{color: tableData.isWarn?'#FFFFFF':'#000000', background: tableData.isWarn?'#E3170D':'white'}">{{tableData.price || '--'}}</li>
    <li>{{tableData.sell || '--'}}</li>
  </ul>
</template>

<script>
export default {
  props: {
    contractCode: {
      type: String,
      default: "BTCUSDT",
    },
  },
  data() {
    return {
      socket: "",
      tableData: {
        code: this.contractCode,
        buy: "",
        price: "",
        sell: "",
      },
      lastPrice: '',
      lastSellPrice: '',
      lastBuyPrice: '',

      date: parseInt(new Date().getTime()/1000),
      errCount: 0,  // 报错次数 
    };
  },
  mounted() {
    this.getNewDate()
    // 初始化
    this.init();
  },
  methods: {
    getNewDate(){
      setTimeout(()=>{
        let newDate =  parseInt(new Date().getTime()/1000)
        if(newDate - this.date > 60){
          this.date = newDate
          this.errCount = 0
        }
        this.getNewDate()
      },1000*60)
    },
    init: function () {
      if (typeof WebSocket === "undefined") {
        alert("您的浏览器不支持socket");
      } else {
        // 实例化socket
        this.socket = new WebSocket(process.env.VUE_APP_WSAPI + "/v1/ws/");
        // 监听socket连接
        this.socket.onopen = this.open;
        // 监听socket错误信息
        this.socket.onerror = this.error;
        // 监听socket消息
        this.socket.onmessage = this.getMessage;
        // 监听socket关闭
        this.socket.onclose = this.close;
      }
    },
    tableRowClassName({ row, rowIndex }) {
     for (const q in this.tableData) {
       if (row.price > row.sell || row.price < row.buy) {
        // if(myVideo){
        //   myVideo.play();
        // }
        return 'info-row'
       }
     }
    },
    open() {
      console.log("socket连接成功");
      this.send(
        JSON.stringify({
          action: "sub",
          topic: "market.contract.switch",
          data: {
            contract_code: this.contractCode,
          },
        })
      );
    },
    error() {
      console.log("连接错误");
    },
    getMessage(msg) {
      let ifpush = true,buyPrice=0,sellPrice=0,newPrice=0;
      let objData = JSON.parse(msg.data)
      if(objData.topic == 'market.ticker'){
        newPrice = objData.data.list[0].trade_price;
        if(newPrice == this.lastPrice){
          ifpush = false 
        }else{
          // this.lastBuyPrice = buyPrice
          // this.lastSellPrice = sellPrice
          this.lastPrice = newPrice
        }
        
        if(ifpush){
          let isWarn = false;
          isWarn = this.lastPrice && this.lastSellPrice && this.lastBuyPrice && (Number(this.lastPrice) > Number(this.lastSellPrice) || Number(this.lastPrice) < Number(this.lastBuyPrice))

          this.tableData = {
            code: this.contractCode,
            buy: this.lastBuyPrice,
            price: this.lastPrice,
            sell: this.lastSellPrice,
            isWarn,
          }
          
          if(isWarn){
            ++this.errCount
            if( this.errCount > 10 ){
              let data = {
                alarm_level: 1,
                content: `【1级】${this.contractCode}合约当前页面最新价格(${this.lastPrice})变动异常，频繁高于委托卖一价${this.lastSellPrice}（或低于委托买一价${this.lastBuyPrice}），存在异常情况，请立即查找原因`,
                id: '--',
                time: new Date(),
              }
              this.$emit('pushErrOne',data)
              this.errCount = 0
            }
          }
        }
      }
      if(objData.topic == 'market.depth'){
        buyPrice = objData.data.buy[0] && objData.data.buy[0].price;
        sellPrice = objData.data.sell[0] && objData.data.sell[0].price;
        // if(buyPrice == this.lastBuyPrice || sellPrice == this.lastSellPrice){
        //   ifpush = false 
        // }else{
          this.lastBuyPrice = buyPrice
          this.lastSellPrice = sellPrice
        // }
        // if(ifpush){
        //   this.tableData = {
        //     code: this.contractCode,
        //     buy: this.lastBuyPrice,
        //     price: this.lastPrice,
        //     sell: this.lastSellPrice,
        //     isWarn: this.lastPrice != '--' && (this.lastPrice > this.lastSellPrice || this.lastPrice < this.lastBuyPrice)
        //   }
        // }
      }
    },
    send(params) {
      this.socket.send(params);
    },
    close(e) {
      console.log("socket已经关闭");
      if(e.code != 4999){
        setTimeout(()=>{
          this.send(
            JSON.stringify({
              action: "sub",
              topic: "market.contract.switch",
              data: {
                contract_code: this.contractCode,
              },
            })
          );
        },5000)
      }
    },
  },
  destroyed() {
    // 销毁监听
    this.socket.close(4999)
  },
};
</script>
<style lang="scss">
.contractMarket_wrap {
  
}
</style>