import Cookies from 'js-cookie'

//命名token名字
const TokenKey = 'platform_token'
const rolesKey = 'p_roles'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(Token<PERSON><PERSON>, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}


export function getRoles() {
  return Cookies.get(TokenKey)? JSON.parse(Cookies.get(rolesKey)):Cookies.get(TokenKey)
}

export function setRoles(token) {
  return Cookies.set(rolesKey, JSON.stringify(token))
}

export function removeRoles() {
  return Cookies.remove(rolesKey)
}