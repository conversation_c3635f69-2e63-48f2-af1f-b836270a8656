<template>
  <div class="legalOrder-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID/昵称"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin-left: 20px; font-size: 12px">申请时间</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime1"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change="filterTimeTransform1"
      >
      </el-date-picker>
      <span style="margin-left: 20px; font-size: 12px">审核时间</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime2"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change="filterTimeTransform2"
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin: 10px 0 0 20px"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="tableList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="userid" align="center" min-width="78">
      </el-table-column>
      <el-table-column
        label="用户名"
        prop="user_name"
        align="center"
        min-width="95"
      >
      </el-table-column>
      <el-table-column
        label="顶级代理ID"
        prop="top_agent_id"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="上级ID"
        prop="pareid"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.pareid || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="订单编号"
        prop="order_id"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.order_id }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="币种"
        prop="coin_name"
        align="center"
        min-width="80px"
      >
      </el-table-column>
      <!-- <el-table-column label="渠道" prop="coin_name" align="center" min-width="80px"> </el-table-column> -->
      <el-table-column
        label="类型"
        prop="order_type"
        align="center"
        min-width="80px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.order_type == 1 ? "购买" : "出售" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="交易金额"
        prop="legal_amount"
        min-width="90px"
        align="center"
      ></el-table-column>
      <el-table-column
        label="交易数量"
        prop="amount"
        min-width="90px"
        align="center"
      ></el-table-column>
      <el-table-column
        label="商家汇率"
        prop="platform_price"
        min-width="120px"
        align="center"
      ></el-table-column>
      <el-table-column
        label="状态"
        prop="netcash"
        min-width="90px"
        align="center"
      >
        <template slot-scope="{ row }">
          <span>{{stypeObj[row.state] ||"--"}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="发起时间"
        prop="create_time"
        width="75"
        align="center"
      >
        <template slot-scope="{ row }">
          <span
            v-html="
              row.create_time
                ? row.create_time.split(' ')[0] +
                  '<br/>' +
                  row.create_time.split(' ')[1]
                : '--'
            "
          ></span>
        </template>
      </el-table-column>
      <el-table-column
        label="处理时间"
        prop="audit_time"
        width="75"
        align="center"
      >
        <template slot-scope="{ row }">
          <span v-if="row.audit_time.indexOf('1970') > -1">--</span>
          <span
            v-else
            v-html="
              row.audit_time
                ? row.audit_time.split(' ')[0] +
                  '<br/>' +
                  row.audit_time.split(' ')[1]
                : '--'
            "
          ></span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        v-if="$store.getters.roles.indexOf('legalorderlist')>-1 && $store.getters.roles.indexOf('legalordercheck')>-1"
        min-width="180px"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button
            v-show="row.state == 0"
            type="primary"
            size="mini"
            @click="handleTgJj(row, true)"
            >通过</el-button
          >
          <el-button
            v-show="row.state == 0"
            type="primary"
            size="mini"
            @click="handleTgJj(row, false)"
            >拒绝</el-button
          >
          <span v-show="row.state != 0">--</span>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <el-dialog
      :title="dialogDetails.handleType ? '通过' : '拒绝'"
      :visible.sync="handleDialog"
      width="30%"
      @closed="handleClose"
    >
      <div class="item_wrap">
        <span>UID：</span><span>{{ dialogDetails.userid }}</span>
      </div>
      <div class="item_wrap">
        <span>币种：</span><span>{{ dialogDetails.coin_name }}</span>
      </div>
      <div class="item_wrap">
        <span>交易数量：</span><span>{{ dialogDetails.amount }}</span>
      </div>
      <div class="item_wrap">
        <span>交易金额：</span><span>{{ dialogDetails.legal_amount }}</span>
      </div>
      <div class="item_wrap">
        <span>商家汇率：</span><span>{{ dialogDetails.platform_price }}</span>
      </div>
      <div class="item_wrap">
        <span>申请时间：</span><span>{{ dialogDetails.create_time }}</span>
      </div>
      <br />
      <div class="item_wrap">
        <span>备注(选填)：</span>
        <el-input v-model="remarks" placeholder="请输入内容"></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmEntry">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { legalorderlist, legalordercheck } from "@/api/fundQuery";
import { getprocoinList } from "@/api/user";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
import { status } from "nprogress";

export default {
  name: "legalOrder",
  data() {
    return {
      listLoading: false,
      total: 0,
      tableList: null,
      filterTime1: [],
      filterTime2: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        star: "", //开始
        end: "", //结束
        checkstar: "", //审核时间开始
        checkend: "", //审核结束结束
        status: undefined, // 状态 （-1全部 0，待审核 1 等待买家付款; 2 买家确认收款／等待卖家发货; 3用户取消; 4 商家取消; 5 超时取消; 6交易完成(已放币）; 7：补充放款;）
        stype: 1, // 2购买 1 出售，
        pageNo: 1,
        pagesize: 10,
      },
      coinOptions: [],
      stypeOptions: [
        { key: 0, name: "待审核" },
        { key: 1, name: "待付款" },
        { key: 2, name: "待放币" },
        // { key: 3, name: "已拒绝" },
        // { key: 4, name: "已拒绝" },
        { key: 8, name: "已拒绝" },
        { key: 5, name: "系统拒绝" },
        { key: 6, name: "已放币" },
        { key: 7, name: "管理员放币" },
      ],
      stypeObj: {
        0: '待审核',
        1: '待付款',
        2: '待放币',
        3: '已拒绝',
        4: '已拒绝',
        5: '系统拒绝',
        6: '已放币',
        7: '管理员放币',
        8: '已拒绝',
      },
      handleDialog: false, // 操作弹框
      dialogDetails: {},
      remarks: "",
    };
  },

  components: {},

  computed: {},

  mounted() {
    // getprocoinList().then((res)=>{
    //   this.coinOptions = res.data.filter(v=>v.status)
    // })
    this.getList();
  },

  methods: {
    confirmEntry() {
      legalordercheck({
        id: this.dialogDetails.order_id,
        pass: this.dialogDetails.handleType,
        content: this.remarks,
      }).then((res) => {
        this.$notify({
          title: "成功",
          message: "操作成功",
          type: "success",
          duration: 2000,
        });
        this.getList();
        this.handleDialog = false;
      });
    },
    handleTgJj(v, type) {
      this.handleDialog = true;
      this.dialogDetails = v;
      console.log(this.dialogDetails);
      this.dialogDetails.handleType = type;
    },
    handleClose() {
      this.remarks = "";
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, {
        status: this.listQuery.status === 0 ? 0 : this.listQuery.status || -1,
      });
      legalorderlist(data).then((res) => {
        this.tableList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform1(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = (val && val[1] + " 23:59:59") || "";
    },
    filterTimeTransform2(val) {
      this.listQuery.checkstar = (val && val[0]) || "";
      this.listQuery.checkend = (val && val[1] + " 23:59:59") || "";
    },
  },
};
</script>
<style lang="scss" scoped>
.legalOrder-container {
  .item_wrap {
    line-height: 30px;
    margin-left: 20px;
  }
}
</style>