<!--角色组内容编辑-->
<template>
  <div class="tjfz">
    <div class="w_menu">
      <span class="searchTitle">分组名称：</span>
      <el-input
        v-model="zname"
        placeholder="请输入分组名称"
        class="inputw"
        clearable
        size="mini"
      ></el-input>
      <el-button :loading="btnLoading" size="mini" type="primary" @click="addHandler" class="buttons">保存</el-button>
    </div>
    <div class="big-box">
      <el-divider content-position="left">欢迎主页</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">主页</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.zhsjChart" >综合数据</el-checkbox>
          <el-checkbox v-model="qx.xzyhChart" >新增用户</el-checkbox>
          <el-checkbox v-model="qx.yhsbChart" >用户设备</el-checkbox>
          <el-checkbox v-model="qx.appxzChart" >app下载</el-checkbox>
        </div>
      </div>
    </div>
    <div class="big-box">
      <el-divider content-position="left">公告管理</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">公告列表</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.gglb"
            @change="()=>{
              qx.ggxg = !qx.gglb?false:qx.ggxg
              qx.ggdel = !qx.gglb?false:qx.ggdel
            }">查看</el-checkbox>
            <el-checkbox
            v-model="qx.ggxg"
            @change="qx.gglb = qx.ggxg || qx.gglb"
          >修改</el-checkbox>
           <el-checkbox
            v-model="qx.ggdel"
            @change="qx.gglb = qx.ggdel || qx.gglb"
          >删除</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">公告发布</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.ggfb">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">首页浮层</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getnoticeimportant"
            @change="()=>{
              qx.noticeimportantadd = !qx.getnoticeimportant?false:qx.noticeimportantadd
              qx.noticeimportantdel = !qx.getnoticeimportant?false:qx.noticeimportantdel
              qx.noticeimportantvalid = !qx.getnoticeimportant?false:qx.noticeimportantvalid
            }">查看</el-checkbox>
            <el-checkbox
            v-model="qx.noticeimportantadd"
            @change="qx.getnoticeimportant = qx.noticeimportantadd || qx.getnoticeimportant"
          >添加/修改</el-checkbox>
           <el-checkbox
            v-model="qx.noticeimportantdel"
            @change="qx.getnoticeimportant = qx.noticeimportantdel || qx.getnoticeimportant"
          >删除</el-checkbox>
           <el-checkbox
            v-model="qx.noticeimportantvalid"
            @change="qx.getnoticeimportant = qx.noticeimportantvalid || qx.getnoticeimportant"
          >上下架</el-checkbox>
        </div>
      </div>
    </div>
    <div class="big-box">
      <el-divider content-position="left">用户查询</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">用户列表</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.list"
            @change="()=>{
              qx.addcont = !qx.list?false:qx.addcont
              qx.whetherrebate = !qx.list?false:qx.whetherrebate
              qx.bcprouserup = !qx.list?false:qx.bcprouserup
              qx.upagentstatus = !qx.list?false:qx.upagentstatus
              qx.setagent = !qx.list?false:qx.setagent
              qx.setagent1 = !qx.list?false:qx.setagent1
              qx.resetuserphone = !qx.list?false:qx.resetuserphone
              qx.setUsersLabel = !qx.list?false:qx.setUsersLabel
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.addcont"
            @change="qx.list = qx.addcont || qx.list"
          >备注</el-checkbox>
          <el-checkbox
            v-model="qx.bcprouserup"
            @change="qx.list = qx.bcprouserup || qx.list"
          >编辑</el-checkbox>
          <el-checkbox 
            v-model="qx.upagentstatus"
            @change="qx.list = qx.upagentstatus || qx.list"
          >解除/开启</el-checkbox>
          <el-checkbox
            v-model="qx.setagent1"
            @change="qx.list = qx.setagent1 || qx.list"
          >提升顶级代理</el-checkbox>
          <el-checkbox
            v-model="qx.setagent"
            @change="qx.list = qx.setagent || qx.list"
          >提升代理</el-checkbox>
          <el-checkbox
            v-model="qx.resetuserphone"
            @change="qx.list = qx.resetuserphone || qx.list"
          >重置</el-checkbox>
          <el-checkbox
            v-model="qx.whetherrebate"
            @change="qx.list = qx.whetherrebate || qx.list"
          >不返佣金/恢复</el-checkbox>
          <el-checkbox
            v-model="qx.setUsersLabel"
            @change="qx.list = qx.setUsersLabel || qx.list"
          >标签/删除标签</el-checkbox>
          <!-- <el-checkbox v-model="qx.bcupverifyreset">资金划转</el-checkbox> -->
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">海外KYC审核</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.verifyhistorylist"
            @change="()=>{
              qx.bcupverifyhistory = !qx.verifyhistorylist?false:qx.bcupverifyhistory
              qx.bcupverifyreset = !qx.verifyhistorylist?false:qx.bcupverifyreset
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.bcupverifyhistory"
            @change="qx.verifyhistorylist = qx.bcupverifyhistory || qx.verifyhistorylist"
          >审核</el-checkbox>
          <el-checkbox 
            v-model="qx.bcupverifyreset"
            @change="qx.verifyhistorylist = qx.bcupverifyreset || qx.verifyhistorylist"
          >重置</el-checkbox>
        </div>
      </div>
    </div>
    <div class="big-box">
      <el-divider content-position="left">交易查询</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">持仓查询</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.positionlist"
            @change="()=>{
              qx.positionlistexport = !qx.positionlist?false:qx.positionlistexport
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.positionlistexport"
            @change="qx.positionlist = qx.positionlistexport || qx.positionlist"
          >导出</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">平仓查询</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.closetradelist"
            @change="()=>{
              qx.closetradeexport = !qx.closetradelist?false:qx.closetradeexport
              qx.closetradelistConfluence = !qx.closetradelist?false:qx.closetradelistConfluence
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.closetradeexport"
            @change="qx.closetradelist = qx.closetradeexport || qx.closetradelist"
          >导出</el-checkbox>
          <el-checkbox 
            v-model="qx.closetradelistConfluence"
            @change="qx.closetradelist = qx.closetradelistConfluence || qx.closetradelist"
          >汇总</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">开仓查询</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.opentradelist"
            @change="()=>{
              qx.opentradeexport = !qx.opentradelist?false:qx.opentradeexport
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.opentradeexport"
            @change="qx.opentradelist = qx.opentradeexport || qx.opentradelist"
          >导出</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">开平仓查询</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.tradelist">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">止盈止损查询</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.plancloseorder">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">计划单查询</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getplanorder">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">用户持仓监控</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getposstioncap">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">导出下载列表</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getmanageexpotlist"
            @change="()=>{
              qx.exprotlistDownload = !qx.getmanageexpotlist?false:qx.exprotlistDownload
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.exprotlistDownload"
            @change="qx.getmanageexpotlist = qx.exprotlistDownload || qx.getmanageexpotlist"
          >导出</el-checkbox>
        </div>
      </div>
    </div>
    <div class="big-box">
      <el-divider content-position="left">跟单查询</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">跟单-持仓查询</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.docpositionlist">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">跟单-平仓查询</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.docclosetradelist"
            @change="()=>{
              qx.followcloseexport = !qx.docclosetradelist?false:qx.followcloseexport
              qx.docclosetradelistConfluence = !qx.docclosetradelist?false:qx.docclosetradelistConfluence
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.followcloseexport"
            @change="qx.docclosetradelist = qx.followcloseexport || qx.docclosetradelist"
          >导出</el-checkbox>
          <el-checkbox 
            v-model="qx.docclosetradelistConfluence"
            @change="qx.docclosetradelist = qx.docclosetradelistConfluence || qx.docclosetradelist"
          >汇总</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">跟单-开仓查询</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.docopentradelist"
            @change="()=>{
              qx.followopenexport = !qx.docopentradelist?false:qx.followopenexport
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.followopenexport"
            @change="qx.docopentradelist = qx.followopenexport || qx.docopentradelist"
          >导出</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">跟单-开平仓查询</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.followtradelist">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">跟单-止盈止损查询</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.docplancloseorder">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">跟单-计划单查询</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.docgetplanorder">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">跟单-资产查询</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.followaccinfo"
            @change="()=>{
              qx.followaccinfoexport = !qx.followaccinfo?false:qx.followaccinfoexport
            }">查看</el-checkbox>
          <el-checkbox 
            v-model="qx.followaccinfoexport"
            @change="qx.followaccinfo = qx.followaccinfoexport || qx.followaccinfo"
          >导出</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">跟单-持仓监控</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getflposstioncap">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">跟单-数据监控</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getfollowcapital">查看</el-checkbox>
        </div>
      </div>
    </div>
    <div class="big-box">
      <el-divider content-position="left">资金查询</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">用户资产查询</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getwellinfo"
            @change="()=>{
              qx.getwellinfoexport = !qx.getwellinfo?false:qx.getwellinfoexport
            }">查看</el-checkbox>
          <el-checkbox 
            v-model="qx.getwellinfoexport"
            @change="qx.getwellinfo = qx.getwellinfoexport || qx.getwellinfo"
          >导出</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">交易资产查询</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getaccinfo"
            @change="()=>{
              qx.advancedsearch = !qx.getaccinfo?false:qx.advancedsearch
              qx.setuserlabl = !qx.getaccinfo?false:qx.setuserlabl
              qx.setuserlabldel = !qx.getaccinfo?false:qx.setuserlabldel
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.advancedsearch"
            @change="qx.getaccinfo = qx.advancedsearch || qx.getaccinfo"
          >高级筛选器</el-checkbox>
          <el-checkbox 
            v-model="qx.setuserlabl"
            @change="qx.getaccinfo = qx.setuserlabl || qx.getaccinfo"
          >添加标签</el-checkbox>
           <el-checkbox 
            v-model="qx.setuserlabldel"
            @change="qx.getaccinfo = qx.setuserlabldel || qx.getaccinfo"
          >删除标签</el-checkbox>
          <el-checkbox 
            v-model="qx.getaccinfoexport"
            @change="qx.getaccinfo = qx.getaccinfoexport || qx.getaccinfo"
          >导出</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">用户出入金查询</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getwalletbill">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">用户财务记录</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getwallerhistory"
            @change="qx.getacchistory = qx.getwallerhistory"
          >查看</el-checkbox>
        </div>
        <div v-show="false" class="big-box-one_2">
          <el-checkbox v-model="qx.getacchistory">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">用户提币管理</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getwithdrawlist"
            @change="()=>{
              qx.withdrawcheck = !qx.getwithdrawlist?false:qx.withdrawcheck
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.withdrawcheck"
            @change="qx.getwithdrawlist = qx.withdrawcheck || qx.getwithdrawlist"
          >审核</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">法币卖出管理</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.legalorderlist"
            @change="()=>{
              qx.legalordercheck = !qx.legalorderlist?false:qx.legalordercheck
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.legalordercheck"
            @change="qx.legalorderlist = qx.legalordercheck || qx.legalorderlist"
          >审核</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">用户数据监控</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getagentcapital">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">PNL查询</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getuserpnl">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">资金费用查询</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getusercaption"
            @change="()=>{
              qx.getusercaptionexport = !qx.getusercaption?false:qx.getusercaptionexport
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.getusercaptionexport"
            @change="qx.getusercaption = qx.getusercaptionexport || qx.getusercaption"
          >导出</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">手续费查询</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getcommiss"
            @change="()=>{
              qx.getcommissexport = !qx.getcommiss?false:qx.getcommissexport
            }"
          >查看</el-checkbox>
          <el-checkbox 
            v-model="qx.getcommissexport"
            @change="qx.getcommiss = qx.getcommissexport || qx.getcommiss"
          >导出</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">每日PNL汇总</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.daypnllist">查看</el-checkbox>
        </div>
      </div>
    </div>
    <div class="big-box">
      <el-divider content-position="left">返佣查询</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">手续费返佣</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getrebotlist"
            @change="()=>{
              qx.sendrebot = !qx.getrebotlist?false:qx.sendrebot
              qx.getuserrebot = !qx.getrebotlist?false:qx.getuserrebot
              qx.getusertrade = !qx.getrebotlist?false:qx.getusertrade
              qx.rebotswitchstatus = !qx.getrebotlist?false:qx.rebotswitchstatus
              qx.getrebotlistexport = !qx.getrebotlist?false:qx.getrebotlistexport
            }">查看</el-checkbox>
          <el-checkbox 
            v-model="qx.getrebotlistexport"
            @change="qx.getrebotlist = qx.getrebotlistexport || qx.getrebotlist"
          >导出</el-checkbox>
          <el-checkbox 
            v-model="qx.sendrebot"
            @change="qx.getrebotlist = qx.sendrebot || qx.getrebotlist"
          >发放/取消发放</el-checkbox>
          <el-checkbox 
            v-model="qx.rebotswitchstatus"
            @change="qx.getrebotlist = qx.rebotswitchstatus || qx.getrebotlist"
          >冻结/恢复</el-checkbox>
          <el-checkbox 
            v-model="qx.getuserrebot"
            @change="()=>{
              qx.getrebotlist = qx.getuserrebot || qx.getrebotlist
              qx.getusertrade = qx.getuserrebot
            }"
          >详情</el-checkbox>
          <el-checkbox v-show="false" v-model="qx.getusertrade">交易记录</el-checkbox>
        </div>
      </div>
    </div>
    <div class="big-box">
      <el-divider content-position="left">平台财务</el-divider>
      <!-- <div class="big-box-one">
        <div class="big-box-one_1">资产概览</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getfinacewallet" >查看</el-checkbox>
        </div>
      </div> -->
      <div class="big-box-one">
        <div class="big-box-one_1">资产账户</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.platwalletlist"
            @change="()=>{
              qx.platwalletdaillist = !qx.platwalletlist?false:qx.platwalletdaillist
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.platwalletdaillist"
            @change="qx.platwalletlist = qx.platwalletdaillist || qx.platwalletlist"
            >历史记录</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">交易账户</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getplataccount"
            @change="()=>{
              qx.plataccountdail = !qx.getplataccount?false:qx.plataccountdail
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.plataccountdail"
            @change="qx.getplataccount = qx.plataccountdail || qx.getplataccount"
            >历史记录</el-checkbox>
        </div>
      </div>
    </div>
    <div class="big-box">
      <el-divider content-position="left">活动管理</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">瓜分手续费</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.bcactivitylist"
            @change="()=>{
              qx.setactivityhash = !qx.bcactivitylist?false:qx.setactivityhash
              qx.setactivitylssued = !qx.bcactivitylist?false:qx.setactivitylssued
              qx.setactivity = !qx.bcactivitylist?false:qx.setactivity
              qx.upuserward = !qx.bcactivitylist?false:qx.upuserward
              qx.getuseractivitylist = !qx.bcactivitylist?false:qx.getuseractivitylist
              qx.useractivitycheck = !qx.bcactivitylist?false:qx.useractivitycheck
            }">查看</el-checkbox>
          <el-checkbox 
            v-model="qx.setactivity"
            @change="qx.bcactivitylist = qx.setactivity || qx.bcactivitylist"
          >设置活动</el-checkbox>
          <el-checkbox 
            v-model="qx.upuserward"
            @change="qx.bcactivitylist = qx.upuserward || qx.bcactivitylist"
          >修改排行榜</el-checkbox>
          <el-checkbox 
            v-model="qx.setactivityhash"
            @change="qx.bcactivitylist = qx.setactivityhash || qx.bcactivitylist"
          >设置HASH</el-checkbox>
          <el-checkbox 
            v-model="qx.setactivitylssued"
            @change="()=>{
              qx.bcactivitylist = qx.setactivitylssued || qx.bcactivitylist
            }"
          >设置奖金</el-checkbox>
          <el-checkbox 
            v-model="qx.getuseractivitylist"
            @change="()=>{
              qx.bcactivitylist = qx.getuseractivitylist || qx.bcactivitylist
            }"
          >查看获奖用户</el-checkbox>
          <el-checkbox 
            v-model="qx.useractivitycheck"
            @change="()=>{
              qx.bcactivitylist = qx.useractivitycheck || qx.bcactivitylist
            }"
          >获奖用户审核</el-checkbox>
        </div>
      </div>
    </div>
    <div class="big-box">
      <el-divider content-position="left">渠道管理</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">顶级代理统计</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.topAgentList">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">代理直推统计</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.agentDirectlist">查看</el-checkbox>
        </div>
      </div>
    </div>
    <div class="big-box">
      <el-divider content-position="left">风控管理</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">标签管理</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.bcprocontractcoinset"
            @change="()=>{
              qx.bcprocontractcoinadd = !qx.bcprocontractcoinset?false:qx.bcprocontractcoinadd
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.bcprocontractcoinadd"
            @change="qx.bcprocontractcoinset = qx.bcprocontractcoinadd || qx.bcprocontractcoinset"
            >操作</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">交易频次</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.frequency"
            @change="()=>{
              qx.setignorerebate = !qx.frequency?false:qx.setignorerebate
              qx.tradefrequencyexport = !qx.frequency?false:qx.tradefrequencyexport
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.setignorerebate"
            @change="qx.frequency = qx.setignorerebate || qx.frequency"
            >操作</el-checkbox>
          <el-checkbox
            v-model="qx.tradefrequencyexport"
            @change="qx.frequency = qx.tradefrequencyexport || qx.frequency"
            >导出</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">高频用户识别</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.highfrequency">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">高盈利用户识别</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.highpnl"
            @change="()=>{
              qx.userpnlexport = !qx.highpnl?false:qx.userpnlexport
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.userpnlexport"
            @change="qx.highpnl = qx.userpnlexport || qx.highpnl"
            >导出</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">高胜率用户识别</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.highwinning"
            @change="()=>{
              qx.highwinningexport = !qx.highwinning?false:qx.highwinningexport
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.highwinningexport"
            @change="qx.highwinning = qx.highwinningexport || qx.highwinning"
            >导出</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">系统监控</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.systemmonitoring">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">同IP行为分析</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.ipstatisticslist"
            @change="()=>{
              qx.ipuserlist = !qx.ipstatisticslist?false:qx.ipuserlist
              qx.iptradelist = !qx.ipstatisticslist?false:qx.iptradelist
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.ipuserlist"
            @change="qx.ipstatisticslist = qx.ipuserlist || qx.ipstatisticslist"
            >IP用户概况</el-checkbox>
          <el-checkbox
            v-model="qx.iptradelist"
            @change="qx.ipstatisticslist = qx.iptradelist || qx.ipstatisticslist"
            >IP交易详情</el-checkbox>
            
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">观察用户列表</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.watchuserlist"
            @change="()=>{
              qx.watchusermark = !qx.watchuserlist?false:qx.watchusermark
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.watchusermark"
            @change="qx.watchuserlist = qx.watchusermark || qx.watchuserlist"
            >操作</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">风控组别列表</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getgrouplist"
            @change="()=>{
              qx.setgrouplist = !qx.getgrouplist?false:qx.setgrouplist
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.setgrouplist"
            @change="qx.getgrouplist = qx.setgrouplist || qx.getgrouplist"
            >操作</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">风控用户列表</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getriskuserlist"
            @change="()=>{
              qx.getristlist = !qx.getriskuserlist?false:qx.getristlist
              qx.getwhitelist = !qx.getriskuserlist?false:qx.getwhitelist
              qx.setwhitelist = !qx.getriskuserlist?false:qx.setwhitelist
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.getristlist"
            @change="qx.getriskuserlist = qx.getristlist || qx.getriskuserlist"
            >历史记录查看</el-checkbox>
          <el-checkbox
            v-model="qx.getwhitelist"
            @change="()=>{
              qx.getriskuserlist = qx.getwhitelist || qx.getriskuserlist
              qx.setwhitelist = !qx.getwhitelist?false:qx.setwhitelist
            }"
            >风控白名单查看</el-checkbox>
          <el-checkbox
            v-model="qx.setwhitelist"
            @change="()=>{
              qx.getriskuserlist = qx.setwhitelist || qx.getriskuserlist
              qx.getwhitelist = qx.setwhitelist || qx.getwhitelist
            }"
            >风控白名单操作</el-checkbox>
        </div>
      </div>
    </div>
    <div class="big-box">
      <el-divider content-position="left">系统管理</el-divider>
       <div class="big-box-one">
        <div class="big-box-one_1">首页Banner配置</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.bannerConfig"
            @change="()=>{
              qx.bannerConfigadd = !qx.bannerConfig?false:qx.bannerConfigadd
              qx.bannerConfigdel = !qx.bannerConfig?false:qx.bannerConfigdel
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.bannerConfigadd"
            @change="qx.bannerConfig = qx.bannerConfigadd || qx.bannerConfig"
            >添加Banner</el-checkbox>
          <el-checkbox
            v-model="qx.bannerConfigdel"
            @change="qx.bannerConfig = qx.bannerConfigdel || qx.bannerConfig"
            >删除Banner</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">角色管理</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.managelist"
            @change="()=>{
              qx.manageadd = !qx.managelist?false:qx.manageadd
              qx.managedel = !qx.managelist?false:qx.managedel
              qx.managesave = !qx.managelist?false:qx.managesave
              qx.managesave1 = !qx.managelist?false:qx.managesave1
              qx.managesave2 = !qx.managelist?false:qx.managesave2
            }">查看</el-checkbox>
            
          <el-checkbox
            v-model="qx.manageadd"
            @change="qx.managelist = qx.manageadd || qx.managelist"
            >添加</el-checkbox>
          <el-checkbox
            v-model="qx.managedel"
            @change="qx.managelist = qx.managedel || qx.managelist"
            >删除</el-checkbox>
          <el-checkbox
            v-model="qx.managesave"
            @change="qx.managelist = qx.managesave || qx.managelist"
            >修改</el-checkbox>
          <el-checkbox
            v-model="qx.managesave1"
            @change="qx.managelist = qx.managesave1 || qx.managelist"
            >重置密码</el-checkbox>
          <el-checkbox
            v-model="qx.managesave2"
            @change="qx.managelist = qx.managesave2 || qx.managelist"
            >重置谷歌</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">权限分组</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.moldelist"
            @change="()=>{
              qx.moldeladd1 = !qx.moldelist?false:qx.moldeladd1
              qx.moldeldel = !qx.moldelist?false:qx.moldeldel
              qx.moldeladd3 = !qx.moldelist?false:qx.moldeladd3
              qx.moldelview = !qx.moldelist?false:qx.moldelview
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.moldeladd1"
            @change="qx.moldelist = qx.moldeladd1 || qx.moldelist"
            >添加分组</el-checkbox>
          <el-checkbox
            v-model="qx.moldeldel"
            @change="qx.moldelist = qx.moldeldel || qx.moldelist"
            >删除</el-checkbox>
          <el-checkbox
            v-model="qx.moldeladd3"
            @change="()=>{
              qx.moldelist = qx.moldeladd3 || qx.moldelist
              qx.moldelview = qx.moldeladd3
            }"
            >修改</el-checkbox>
          <el-checkbox v-show="false" v-model="qx.moldelview">详情</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">操作日志</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getmanagelogs">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">CRM操作日志</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getagentoplog">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">联系我们</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.contactUs">查看</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">用户日志</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.userlog"
            @change="()=>{
              qx.getuserlogexport = !qx.userlog?false:qx.getuserlogexport
            }">查看</el-checkbox>
          <el-checkbox
            v-model="qx.getuserlogexport"
            @change="qx.userlog = qx.getuserlogexport || qx.userlog"
            >导出</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">反馈数据</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.usererrlog">查看</el-checkbox>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { moldeladd, moldelview } from "@/api/systemAdminister";
export default {
  name: "addGroup",
  data() {
    return {
      zname: "",
      id: "",
      type: "1",
      qx: {
        // 首页
        zhsjChart: false,  // 综合数据 图表
        xzyhChart: false,  // 新增用户 图表
        yhsbChart: false,  // 用户设备 图表
        appxzChart: false,  // app下载 图表

        //公告管理
        // notice:false,
        gglb:false,
        ggfb:false,
        ggxg:false,
        ggdel:false,
        getnoticeimportant: false, // 首页浮层 - 列表
        noticeimportantadd: false, // 首页浮层 - 添加/修改
        noticeimportantdel: false, // 首页浮层 - 删除
        noticeimportantvalid: false, // 首页浮层 - 上下架
        //用户列表
        list: false,            //查看
        bcprouserup: false,     //编辑 用户编辑
        addcont: false,         //备注
        setagent1: false,        //提升顶级代理
        setagent: false,        //提升代理
        upagentstatus: false,   //解除/开启
        bcupverifyreset: false, //资金划转
        resetuserphone: false,  //重置
        whetherrebate: false,  //不设返佣
        setUsersLabel: false, // 设置标签
        // 海外kfc审核
        verifyhistorylist: false, //查看
        bcupverifyhistory: false, //审核
        bcupverifyreset: false,   //重置

        // 交易查询
        positionlist: false,    // 持仓查询 - 查看
        positionlistexport: false,    // 持仓查询 - 导出
        closetradelist: false,  // 平仓查询 - 查看
        closetradeexport: false, // 平仓查询 - 导出
        closetradelistConfluence: false, // 平仓查询 - 汇总
        opentradelist: false,   // 开仓查询 - 查看
        opentradeexport: false, // 开仓查询 - 导出
        tradelist: false, // 开平仓查询  
        plancloseorder: false,   // 止盈止损查询 - 查看
        getplanorder: false,   // 计划单查询 - 查看
        getposstioncap: false,   // 用户持仓监控 - 查看
        getmanageexpotlist: false,   // 导出下载列表 - 查看
        exprotlistDownload: false,  // 导出下载列表 - 下载

         //跟单查询
        docpositionlist: false,    // 持仓查询 - 查看
        docclosetradelist: false,  // 平仓查询 - 查看
        followcloseexport: false,  // 平仓查询 - 导出
        docclosetradelistConfluence: false, // 平仓查询 - 汇总
        docopentradelist: false,   // 开仓查询 - 查看
        followopenexport: false,   // 开仓查询 - 导出
        followtradelist: false,  // 开平仓查询 - 查看
        docplancloseorder:false, // 止盈止损查询 -查看
        docgetplanorder:false, //计划单查询 -查看
        followaccinfo:false, //资产查询 -查看
        getflposstioncap: false,   // 用户持仓监控 - 查看

        //用户资产查询
        getwellinfo: false, //查看
        //交易资产查询
        getaccinfo: false, //查看
        advancedsearch: false, //高级筛选器
        setuserlabl: false, //添加标签
        setuserlabldel:false,
        // 用户出入金查询
        getwalletbill: false, //查看
        // 用户财务记录
        getwallerhistory: false, // 钱包账户财务记录 查看
        getacchistory: false, // 合约账户财务记录 查看
        // 用户提币管理
        getwithdrawlist: false, //查看
        withdrawcheck: false, //审核
        // 法币卖出管理
        legalorderlist: false, //查看
        legalordercheck: false, //审核
        // PNL查询
        getuserpnl: false, //查看
        // 资金费用查询
        getusercaption: false, //查看
        getusercaptionexport: false, //导出
        // 手续费查询
        getcommiss: false, //查看
        getcommissexport: false, //导出
        // 用户数据监控
        getagentcapital: false, //查看
        // 每日PNL汇总
        daypnllist: false, //查看
        // 手续费返佣
        getrebotlist: false, //查看
        getrebotlistexport: false, //导出
        sendrebot: false, // 发放
        rebotswitchstatus: false, // 冻结
        getuserrebot: false, // 详情
        getusertrade: false, // 用户返佣交易记录
        // // 资产概览
        // getfinacewallet: false, // 查看
        
        platwalletlist: false, ////资产账户 查看
        platwalletdaillist: false, //历史记录
        
        getplataccount: false, //// 交易账户 查看
        plataccountdail: false, //历史记录
        // 活动管理
        bcactivitylist: false, // 瓜分手续费 查看
        setactivity: false, // 瓜分手续费 设置活动
        upuserward: false, // 修改排行
        setactivityhash: false, // 瓜分手续费 设置哈希
        setactivitylssued: false, // 瓜分手续费 设置奖金
        getuseractivitylist: false, //瓜分手续费 获取用户获奖列表
        useractivitycheck: false, //瓜分手续费 获取用户获奖列表 -审核
        //渠道管理
        topAgentList: false, //顶级代理统计  查看
        agentDirectlist: false, //代理直推统计  查看
        //标签管理
        bcprocontractcoinset: false, //查看
        bcprocontractcoinadd: false, //操作
        // 交易频次
        frequency:false, // 查看
        setignorerebate :false, // 去除返佣
        // 高频用户识别
        highfrequency:false, // 查看
        // 高盈利用户识别
        highpnl:false, // 查看
        userpnlexport: false, // 导出
        // 高胜率用户识别
        highwinning:false, // 查看
        highwinningexport: false, // 导出
        // 系统监控
        systemmonitoring:false, // 查看
        // 同IP行为分析
        ipstatisticslist:false, // 查看
        ipuserlist: false, // 同IP - IP用户概况
        iptradelist: false, // 同IP - IP交易详情
        // 观察用户列表
        watchuserlist:false, // 查看
        watchusermark: false, // 操作
        getgrouplist:false, // 风控组别列表 查看
        setgrouplist: false, // 风控组别列表 操作
        getriskuserlist:false, // 风控用户列表 查看
        getristlist: false, // 风控用户列表 历史记录
        getwhitelist: false, // 风控用户列表 风控白名单
        
        // Banner配置
        bannerConfig:false,
        bannerConfigdel:false,
        bannerConfigadd:false,
        //角色管理
        // bannerConfig:false,//bannne配置
        managelist: false, //查看
        
        manageadd: false, //添加账号  账号管理-账号列表
        managedel: false, //删除  账号管理-账号列表
        managesave: false, //修改  账号管理-账号列表
        managesave1: false, //重置登陆密码  账号管理-账号列表
        managesave2: false, //重置谷歌密码  账号管理-账号列表
        //权限分组
        moldelist: false, //查看
        moldeladd1: false, //添加分组
        moldeladd3: false, //修改分组
        moldeldel: false, //删除
        moldelview: false, //详情
        //操作日志
        getmanagelogs: false, //查看
        //CRM 操作日志
        getagentoplog: false, //查看
        // 用户日志
        userlog: false, // 查看
        getuserlogexport: false, // 导出
        // 反馈数据
        usererrlog: false, // 查看
        //联系我们
        contactUs:false//查看
      },
      btnLoading: false,
    };
  },
  components: {},
  mounted() {
    if (this.$route.query.type == "1") {
      // 获取详情
      moldelview({ upid: parseInt(this.$route.query.id) }).then((res) => {
        let mode = JSON.parse(JSON.parse(res.data.modedata));
        Object.assign(this.qx, mode);
      })
    }
    this.type = this.$route.query.type;
    if (this.$route.query.name) {
      this.zname = this.$route.query.name;
    }
  },
  computed: {},
  methods: {
    addHandler() {
      if (this.type == "1") {
        moldeladd({model_data: JSON.stringify(this.qx), model_name: this.zname, type: 3, modelid: parseInt(this.$route.query.id),}).then((res) => {
          this.btnLoading = false;
          // console.log(this.qx)
          setTimeout(() => {
            this.$router.go(-1);
            this.$notify({ title: "成功", message: res.msg, type: "success" });
          }, 0.375 * 1000);
        });
      } else {
        let t = false;
        for (let pro in this.qx) {
          if (this.qx[pro]) {
            t = true;
          }
        }
        if (this.zname == null || this.zname == "") {
          this.$notify({
            title: "警告",
            message: "请填写分组名称",
            type: "warning"
          });
        } else if (!t) {
          this.$notify({ title: "警告", message: "请勾选权限", type: "warning" });
        } else {
          moldeladd({model_data: JSON.stringify(this.qx), model_name: this.zname, type: 1,}).then((res) => {
            this.btnLoading = false;
            setTimeout(() => {
              this.$router.go(-1);
              this.$notify({ title: "成功", message: res.msg, type: "success" });
            }, 0.375 * 1000);
          });
        }
      }
    },
    // isShow() {
    //   let level = localStorage.getItem("level");
    //   level = JSON.parse(level);
    //   return level === "1";
    // },
    // message(tag, data) {
    //   switch (tag.dataType) {
    //     case GETJSFZXQ:
    //       if (data.data) {
    //         let mode = JSON.parse(JSON.parse(data.data.modedata));
    //         Object.assign(this.qx, mode);
    //         // console.log(typeof mode)
    //       }
    //       break;
    //     case TJFZ: //添加分组
    //       if (data.ret === 0) {
    //         this.$router.go(-1);
    //         this.$notify({ title: "成功", message: data.msg, type: "success" });
    //       }
    //       break;
    //     case XGFZ2: //修改分组内容
    //       if (data.ret === 0) {
    //         this.$router.go(-1);
    //         this.$notify({ title: "成功", message: data.msg, type: "success" });
    //       }
    //       break;
    //   }
    // }
  },

};
</script>

<style lang="scss" scoped>
.big-box:nth-of-type(2) {
  margin-top: 20px;
}
.big-box {
  width: 99%;
  // border-top: 1px solid #f0f2f5;
  display: flex;
  flex-direction: column;
  // align-items: center;
  margin-top: 20px;
  justify-content: space-around;
  ::v-deep .el-divider--horizontal{
    margin-top: 10px;
    margin-bottom: 12px;
    .el-divider__text{
      color: #000;
      font-size: 14px;
      font-weight: bold;
    }
  }
  .big-box-one {
    display: flex;
    align-items: center;
    margin: 5px;
    .big-box-one_1 {
      min-width: 130px;
      font-size: 13px;
    }
    .big-box-one_2 {
      margin-left: 20px;
    }
  }
}
</style>

<style lang="scss" scoped>
.inputw {
  width: 200px;
  padding-right: 30px;
}
.name_span {
  padding-top: 2px;
}
.inputs {
  padding-right: 30px;
}

.buttons {
  margin-left: 20px;
}

.checkboxp {
  padding: 3px 0;
}

.dc {
  display: flex;
  padding: 10px 0px;
  box-sizing: border-box;
  border-bottom: 1px solid#F0F2F5;
  width: 100%;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  // flex-direction: column;
}

.tjfz {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px 30px;
  .w_menu {
    width: 100%;
    height: 50px;
    // padding-left:15px;
    padding-top: 15px;
    padding-bottom: 15px;
    flex-shrink: 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .searchTitle {
      padding: 0;
    }
  }
  .zc {
    & > div {
      display: flex;
    }
  }
  .w_c1 {
    .dc {
      display: flex;
      padding: 10px 0px;
      box-sizing: border-box;
      border-bottom: 1px solid#F0F2F5;
      width: 100%;
      justify-content: flex-start;
      align-items: flex-start;
      flex-wrap: wrap;
      flex-direction: column;
    }
    // display: flex;

    // margin-left: 30px;
    margin-top: 10px;
    flex-direction: column;
    border-bottom: 1px solid grey;
    .titlespanb {
      text-align: left;
      font-weight: 900;
      padding: 2px 0px;
    }
    .w_c2 {
      display: flex;
      flex-wrap: wrap;
      padding: 2px 0px;
      //   margin-top: ;
    }
    .itemdiv {
      height: 35px;
      // display: flex;
      min-width: 300px;
      padding: 4px 0;
      //   margin-top: 10px;
      .titlespan {
        display: inline-block;
        width: 300px;
        text-align: left;
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
  .w_page {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-top: 10px;
    padding-right: 30px;
  }

  .dialogc {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .dialogstip {
    color: red;
    padding: 20px 10px;
  }
}
</style>
