<template>
  <div class="asset-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">时间</span>
        <!-- :picker-options="pickerOptions" -->
      <el-date-picker
        style="width: 220px; margin-top: 5px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change="filterTimeTransform"
      />
      <el-button
        style="margin-top: 10px; margin-left: 10px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <!-- <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('') > -1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        导出
      </el-button> -->
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.real_name || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="UID" prop="user_id" align="center" min-width="78"></el-table-column>
      <el-table-column
        label="合约PNL"
        prop="pnl"
        align="center"
        min-width="125"
      >
      </el-table-column>
      <el-table-column
        label="跟单手续费"
        prop="follow_commission"
        align="center"
        min-width="125"
      >
      </el-table-column>
      <el-table-column
        label="跟单PNL"
        prop="follow_pnl"
        align="center"
        min-width="125"
      >
      </el-table-column>
      <el-table-column
        label="返佣比例"
        prop="agent_rebate_ratio"
        align="center"
        min-width="125"
      >
        <template slot-scope="{row}">
          <span>{{row.agent_rebate_ratio+'%'}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="返佣金额"
        prop="agent_rebate_commission"
        align="center"
        min-width="125"
      >
      </el-table-column>
      <el-table-column
        label="P1"
        prop="p1"
        align="center"
        min-width="125"
      >
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200, 100, 50, 20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 封装api
import { topagentlist } from "@/api/channelManage";

export default {
  name: "topAgentList",
  data() {
    return {
    //   pickerOptions: {
    //     disabledDate: (time) => {
    //       let defalutStartTime = new Date().getTime() - 30 * 24 * 3600 * 1000; // 转化为时间戳
    //       return (
    //         time.getTime() >= Date.now() || time.getTime() <= defalutStartTime
    //       );
    //     },
    //   },
      listLoading: false,
      total: 0,
      filterTime: [],
      tableData: null,
      listQuery: {
        sectop: "", //顶级代理id
        pageNo: 1,
        pagesize: 20,
      },
      exportLoading: false, //导出加载中效果
    };
  },
  components: {},

  computed: {
    // 默认时间
    timeDefault() {
      let date = new Date();
      // 通过时间戳计算
      let defalutStartTime = (
        (date.getTime() - 1 * 24 * 3600 * 1000) /
        1000
      ).toDate("yyyy-MM-dd"); // 转化为时间戳
      let defalutEndTime = (date.getTime() / 1000).toDate("yyyy-MM-dd");
      return [defalutStartTime, defalutStartTime];
    },
  },

  mounted() {
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery);
      data.star = (this.filterTime && this.filterTime[0] + " 00:00:00") || "2019-01-01 00:00:00";
      data.end = (this.filterTime && this.filterTime[1] + " 23:59:59") || (new Date().getTime() / 1000).toDate("yyyy-MM-dd HH:mm:ss"); 
      topagentlist(data).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {};
      Object.assign(data, this.listQuery);
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime ? this.filterTime[1] : "";
      userpnlexport(data)
        .then((res) => {
          if (res.ret == 0) {
            this.$notify.success({
              title: "操作成功",
              message: "请到‘交易查询/导出下载列表’进行下载",
            });
            this.exportLoading = false;
          }
        })
        .catch((err) => {
          this.exportLoading = false;
        });
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0] + " 00:00:00") || "";
      this.listQuery.end = (val && val[1] + " 23:59:59") || "";
    },
  },
};
</script>
<style lang="scss" scoped>
.asset-container {
  .filter-container {
    .highSwitch_wrap {
      margin-top: 15px;
      width: 100px;
      cursor: pointer;
      font-size: 14px;
    }
  }
  .select_wrap {
    // padding: 0 30px 0 30px;
    display: flex;
    align-items: center;
    span {
      width: 100px;
      // padding-right: 20px;
    }
  }
}
</style>