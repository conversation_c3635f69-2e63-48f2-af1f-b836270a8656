<template>
  <div class="asset-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.groupid"
        placeholder="风控组别"
        clearable
        style="width: 150px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in grouplist"
          :key="item.id"
          :label="item.group_name"
          :value="item.id"
        />
      </el-select>
      <el-button
        style="margin-top: 10px; margin-left: 10px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        style="float: right"
        class="filter-item"
        v-if="$store.getters.roles.indexOf('getristlist') > -1"
        @click="$router.push('/riskAdminister/riskuserhistory')"
        size="mini"
        type="success"
      >
        历史记录
      </el-button>
      <el-button
        style="float: right"
        class="filter-item"
        v-if="$store.getters.roles.indexOf('getwhitelist') > -1"
        @click="$router.push('/riskAdminister/riskuserwhite')"
        size="mini"
        type="success"
      >
        风控白名单
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="user_id" align="center" min-width="125">
      </el-table-column>
      <el-table-column
        label="用户名"
        prop="user_name"
        align="center"
        min-width="125"
      >
      </el-table-column>
      <el-table-column
        label="顶级代理"
        prop="group_id"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.group_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="风控组别"
        prop="group_name"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.group_name || "--" }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || "--" }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="上级ID"
        prop="pareid"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.pareid || "--" }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || "--" }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="总入金"
        prop="total_in"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.total_in || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="总出金"
        prop="total_out"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.total_out || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="净入金"
        prop="net_cash"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.net_cash || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="平仓笔数"
        prop="close_num"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.close_num || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="PNL"
        prop="pnl"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.pnl || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="手续费"
        prop="commiss"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.commiss || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="资金费用"
        prop="cap_amount"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.cap_amount || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="净PNL"
        prop="net_pnl"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.net_pnl || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="盈利率"
        prop="warn_profit"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.warn_profit || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="胜率"
        prop="warn_float"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.warn_float || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="手续费占净入金比率"
        prop="commiss_cash"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.commiss_cash || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="占用保证金"
        prop="bond"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.bond || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="标签"
        prop="label_name"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <span>{{ row.label_name || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="白名单状态"
        prop="status"
        align="center"
        min-width="125"
      >
      <template slot-scope="{ row }">
          <span>{{ row.status === 1 ? '是' : '否'}}</span>
      </template>
      </el-table-column>
      <!-- <el-table-column
        label="最近24H净PNL"
        prop="within24_pnl"
        align="center"
        min-width="125"
      >
      </el-table-column> -->
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200, 100, 50, 20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 封装api
import { getriskuserlist, getgrouplist } from "@/api/riskAdminister";

export default {
  name: "riskuserlist",
  data() {
    return {
      listLoading: false,
      total: 0,
      filterTime: [],
      tableData: null,
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        orderside: 3, //排序
        stype: 2, // 1正序 2反序
        pageNo: 1,
        pagesize: 20,
      },
      grouplist: [], // 组别列表
    };
  },
  components: {},

  computed: {
    // 默认时间
    timeDefault() {
      let date = new Date();
      // 通过时间戳计算
      let defalutStartTime = (
        (date.getTime() - 1 * 24 * 3600 * 1000) /
        1000
      ).toDate("yyyy-MM-dd"); // 转化为时间戳
      let defalutEndTime = (date.getTime() / 1000).toDate("yyyy-MM-dd");
      return [defalutStartTime, defalutStartTime];
    },
  },

  mounted() {
    this.filterTime = this.timeDefault;
    getgrouplist({pageNo: 1,pagesize: 10000 }).then(res=>{
      this.grouplist = res.data.list
    })
    this.getList();
  },

  methods: {
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery,);
      getriskuserlist(data).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.asset-container {
  .filter-container {
    .highSwitch_wrap {
      margin-top: 15px;
      width: 100px;
      cursor: pointer;
      font-size: 14px;
    }
  }
  .select_wrap {
    // padding: 0 30px 0 30px;
    display: flex;
    align-items: center;
    span {
      width: 100px;
      // padding-right: 20px;
    }
  }
}
</style>