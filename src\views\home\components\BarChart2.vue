<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

const animationDuration = 1000

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ dataList1, dataList2, dataList3, dataTime,  } = {}) {
      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          left: 10,
          right: 10,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: dataTime,
          axisTick: {
            alignWithLabel: true
          }
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false
          }
        }],
        legend: {
          top: 'top',
          data: ['IOS-极速下载','IOS-本地下载', '安卓-极速下载', '安卓-本地下载',]
        },
        series: [{
          name: 'IOS-极速下载',
          type: 'bar',
          stack: 'vistors',
          barWidth: '60%',
          data: dataList1,
          animationDuration
        }, {
          name: 'IOS-本地下载',
          type: 'bar',
          stack: 'vistors',
          barWidth: '60%',
          data: dataList2,
          animationDuration
        }, {
          name: '安卓-极速下载',
          type: 'bar',
          stack: 'vistors',
          barWidth: '60%',
          data: dataList3,
          animationDuration
        }, {
          name: '安卓-本地下载',
          type: 'bar',
          stack: 'vistors',
          barWidth: '60%',
          data: dataList3,
          animationDuration
        }]
      })
    }
  }
}
</script>
