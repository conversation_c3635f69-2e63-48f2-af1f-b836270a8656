import request from '@/utils/request'

//活动列表
export function bcactivitylist(data) {
  return request({
    url: '/managers/v1/activity/bcactivitylist',
    method: 'post',
    data: { data }
  })
}
//设置哈希
export function setactivityhash(data) {
  return request({
    url: '/managers/v1/activity/setactivityhash ',
    method: 'post',
    data: { data }
  })
}
//设置奖池金额
export function setactivitylssued(data) {
  return request({
    url: '/managers/v1/activity/setactivitylssued ',
    method: 'post',
    data: { data }
  })
}
//获取活动排行
export function getuserwardlist(data) {
  return request({
    url: '/managers/v1/activity/getuserwardlist',
    method: 'post',
    data: { data }
  })
}
//插入排行
export function inuserwardlist(data) {
  return request({
    url: '/managers/v1/activity/inuserwardlist',
    method: 'post',
    data: { data }
  })
}
//修改排行
export function upuserward(data) {
  return request({
    url: '/managers/v1/activity/upuserward',
    method: 'post',
    data: { data }
  })
}
//获取用户获奖列表
export function getuseractivitylist(data) {
  return request({
    url: '/managers/v1/activity/getuseractivitylist',
    method: 'post',
    data: { data }
  })
}
//设置活动
export function setactivity(data) {
  return request({
    url: '/managers/v1/activity/setactivity',
    method: 'post',
    data: { data }
  })
}
//获取当前活动设置
export function getnowactivity(data) {
  return request({
    url: '/managers/v1/activity/getnowactivity',
    method: 'post',
    data: { data }
  })
}
//奖励审核
export function useractivitycheck(data) {
  return request({
    url: '/managers/v1/activity/useractivitycheck',
    method: 'post',
    data: { data }
  })
}
