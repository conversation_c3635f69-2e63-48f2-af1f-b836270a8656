<template>
  <div class="kfc-container">
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 20px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="类型" prop="stype" align="center" min-width="105">
        <template slot-scope="{row}">
          <span>{{typeOptions[row.stype] || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" prop="stype" align="center" min-width="105">
        <template slot-scope="{row}">
          <span>{{statusOptions[row.status] || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="creat_at" align="center" width="160px"> </el-table-column>
      <el-table-column label="开始时间" prop="star_time" align="center" width="160px"> </el-table-column>
      <el-table-column label="结束时间" prop="end_time" align="center" width="160px"> </el-table-column>
      <el-table-column label="操作人" prop="manage" align="center" min-width="75">
        <template slot-scope="{ row }">
          <span>{{ row.manage || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        min-width="130px"
        class-name="small-padding fixed-width"
        align="center"
        v-if="$store.getters.roles.indexOf('exprotlistDownload')>-1"
      >
        <template slot-scope="{ row }">
          <el-button
            v-if="$store.getters.roles.indexOf('exprotlistDownload')>-1 && row.status == 1"
            type="primary"
            size="mini"
            @click="exportHandle(row)"
          >
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//引入封装接口
import {
  getmanageexpotlist,
} from "@/api/transactionQuery";

const typeOptions = {
  1: "交易平仓 导出",
  2: "交易开仓 导出",
  3: "交易资产 导出", 
  4: "用户资产 导出", 
  5: "跟单资产 导出", 
  6: "跟单平仓 导出", 
  7: "跟单开仓 导出", 
  8: "资金费用 导出", 
  9: "手续费 导出",
  10: "交易频次 导出",
  11: "高频用户识别 导出",
  12: "高胜率用户识别 导出",
  13: "高盈利用户识别 导出",
};
const statusOptions = {
  0: '导出中',
  1: '导出成功',
  2: '导出失败'
};
export default {
  name: "exportList",
  data() {
    
    return {
      total: 0,
      tableData: [],
      listLoading: true,
      listQuery: {
        pageNo: 1,
        pagesize: 20,
      },
      updata: {
        
      },
      kycstate:{
          //备注
      content:''
      },
      updatas:[],
      ResetDialogVisible: false,//重置弹框显示控制
      typeOptions,//审核状态 Options
      statusOptions,
      stat:[],
      statIndex:[],
      rules:{
        audit_results:[
          { required: true,message:'请选择', trigger: ['blur','change'] },
        ],
        err_info:[
          { required: true,message:'必填项', trigger: ['blur'] },
        ]
      },
    
      //判断审核弹框里面是拒绝还是通过
      kycselet:false
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    // 回车搜索事件
    handleFilter(){
      this.getList();
    },
    //  table列表数据
    getList() {
      //开始有加载中效果
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      data.ttype = data.ttype === ''?-1:data.ttype
      getmanageexpotlist(data).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
        // console.log(this.stat)
      });
    },
    exportHandle(row){
      window.location.href = row.url;
    },  
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 0 10px;
 
  // justify-content: space-around;
  .wc_1-one {
    width: 45%;
  }
  .idPhotoImg_wrap ::v-deep .el-image__inner{
    width: auto;
   
  }
  .idPhotoImg_wrap ::v-deep .el-image__error{
    min-width: 100px;
  }
}
</style>