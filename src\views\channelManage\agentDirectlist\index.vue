<template>
  <div class="asset-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.user_id"
        size="mini"
        placeholder="UID"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.agentid"
        size="mini"
        placeholder="上级代理UID"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">时间</span>
        <!-- :picker-options="pickerOptions" -->
      <el-date-picker
        style="width: 220px; margin-top: 5px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change="filterTimeTransform"
      />
      <el-button
        style="margin-top: 10px; margin-left: 10px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <!-- <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('') > -1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        导出
      </el-button> -->
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="user_id" align="center" min-width="78"></el-table-column>
      <el-table-column label="注册人数" prop="register_amount" align="center" min-width="78"></el-table-column>
      <el-table-column label="净入金" prop="pure_deposit" align="center" min-width="78"></el-table-column>
      <el-table-column label="交易额" prop="turnover" align="center" min-width="78"></el-table-column>
      <el-table-column label="手续费" prop="commission" align="center" min-width="78"></el-table-column>
      <el-table-column label="返佣比例" prop="agent_rebate_ratio" align="center" min-width="78"></el-table-column>
      <el-table-column label="返佣金额" prop="agent_rebate_commission" align="center" min-width="78"></el-table-column>
      <el-table-column label="PNL" prop="pnl" align="center" min-width="78"></el-table-column>
      <el-table-column label="净PNL" prop="pure_pnl" align="center" min-width="78"></el-table-column>
      <el-table-column label="P1" prop="user_id" align="center" min-width="78">
        <template slot-scope="{row}">
          <span>{{row.pnl - row.agent_rebate_commission}}</span>
        </template>
      </el-table-column>
      <el-table-column label="P2" prop="user_id" align="center" min-width="78">
        <template slot-scope="{row}">
          <span>{{row.pnl && (row.commission / row.pnl) || 0}}</span>
        </template>
      </el-table-column>
      <el-table-column label="盈利人数/占比" prop="profit_person" align="center" min-width="78">
        <template slot-scope="{row}">
          <span>{{row.profit_person+"/"+((row.trade_person && (row.profit_person / row.trade_person)*100) || 0)+'%'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="人均交易次数" prop="trade_num" align="center" min-width="78"></el-table-column>
      <el-table-column label="日活" prop="dau" align="center" min-width="78"></el-table-column>
      <el-table-column label="冻结保证金" prop="margin" align="center" min-width="78"></el-table-column>
      <el-table-column label="浮动盈亏" prop="float_profit" align="center" min-width="78"></el-table-column>
      <el-table-column label="注册时间" prop="reg_time" align="center" min-width="78"></el-table-column>
      <!-- <el-table-column label="操作" align="center" width="100">
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            type="primary"
            @click="detailClick(row)"
            >查看</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200, 100, 50, 20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 封装api
import { agenttotallist } from "@/api/channelManage";

export default {
  name: "agentDirectlist",
  data() {
    return {
    //   pickerOptions: {
    //     disabledDate: (time) => {
    //       let defalutStartTime = new Date().getTime() - 30 * 24 * 3600 * 1000; // 转化为时间戳
    //       return (
    //         time.getTime() >= Date.now() || time.getTime() <= defalutStartTime
    //       );
    //     },
    //   },
      listLoading: false,
      total: 0,
      filterTime: [],
      tableData: null,
      listQuery: {
        user_id: '',   // 用户ID
        agentid: '',   //代理ID
        pageNo: 1,
        pagesize: 20,
      },
      exportLoading: false, //导出加载中效果
    };
  },
  components: {},

  computed: {
    // 默认时间
    timeDefault() {
      let date = new Date();
      // 通过时间戳计算
      let defalutStartTime = (
        (date.getTime() - 1 * 24 * 3600 * 1000) /
        1000
      ).toDate("yyyy-MM-dd"); // 转化为时间戳
      let defalutEndTime = (date.getTime() / 1000).toDate("yyyy-MM-dd");
      return [defalutStartTime, defalutStartTime];
    },
  },

  mounted() {
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    //点击查看跳转详情页
    detailClick(row){
      // var user_id = JSON.parse(row.user_id)
      this.$router.push({
        path: "/riskAdminister/highwinningdetails",
        query: { 
          id:JSON.parse(row.user_id),
          details: JSON.stringify(row)
        },
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery);
      data.star = (this.filterTime && this.filterTime[0] + " 00:00:00") || "1970-01-01 00:00:00";
      data.end = (this.filterTime && this.filterTime[1] + " 23:59:59") || (new Date().getTime() / 1000).toDate("yyyy-MM-dd HH:mm:ss"); 
      agenttotallist(data).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {};
      Object.assign(data, this.listQuery);
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime ? this.filterTime[1] : "";
      userpnlexport(data)
        .then((res) => {
          if (res.ret == 0) {
            this.$notify.success({
              title: "操作成功",
              message: "请到‘交易查询/导出下载列表’进行下载",
            });
            this.exportLoading = false;
          }
        })
        .catch((err) => {
          this.exportLoading = false;
        });
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0] + " 00:00:00") || "";
      this.listQuery.end = (val && val[1] + " 23:59:59") || "";
    },
  },
};
</script>
<style lang="scss" scoped>
.asset-container {
  .filter-container {
    .highSwitch_wrap {
      margin-top: 15px;
      width: 100px;
      cursor: pointer;
      font-size: 14px;
    }
  }
  .select_wrap {
    // padding: 0 30px 0 30px;
    display: flex;
    align-items: center;
    span {
      width: 100px;
      // padding-right: 20px;
    }
  }
}
</style>