<template>
  <div class="role-container">
    <div class="filter-container">
      <div>
        <el-input
          size="mini"
          v-model="listQuery.name"
          placeholder="用户名"
          style="width: 150px; margin-right: 20px"
          clearable
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <!-- <el-select
          size="mini"
          v-model="listQuery.modelid"
          placeholder="分组名称"
          clearable
          style="width: 120px; margin-left: 20px"
          class="filter-item"
        >
          <el-option
            v-for="item in options"
            :key="item.model_id"
            :label="item.modelname"
            :value="item.model_id">
          </el-option>
        </el-select> -->
        <!-- <span style="margin-left: 20px; font-size: 12px">创建时间</span>
        <el-date-picker
          style="width: 220px; margin-top: 10px"
          v-model="listQuery.transaction_time"
          size="mini"
          type="daterange"
          range-separator="-"
          start-placeholder="起始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker> -->
        <el-button
          class="filter-item"
          size="mini"
          type="primary"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button size="mini" type="success" v-if="$store.getters.roles.indexOf('manageadd')>-1" @click="createClick()"
          >添加账号</el-button
        >
      </div>
    </div>

    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column type="index" align="center"></el-table-column>
      <el-table-column prop="name" align="center" label="用户名" > </el-table-column>
      <el-table-column prop="model_name" align="center" label="角色分组" > </el-table-column>
      <el-table-column prop="reg_time" align="center" label="创建时间"> </el-table-column>
      <el-table-column prop="is_view" align="center" label="是否脱敏">
        <template slot-scope="{ row }">
          <span>{{row.is_view?'是':'否'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作"  align="center" width="330" v-if="$store.getters.roles.indexOf('managedel')>-1 || $store.getters.roles.indexOf('managesave')>-1 || $store.getters.roles.indexOf('managesave1')>-1 || $store.getters.roles.indexOf('managesave2')>-1" >
        <template slot-scope="{ row, $index }">
          <el-button size="mini" type="primary" v-if="$store.getters.roles.indexOf('managedel')>-1"  @click="deleteClick(row, $index)"
            >删除</el-button
          >
          <el-button size="mini" type="primary" v-if="$store.getters.roles.indexOf('managesave')>-1" @click="updataClick(row)">修改</el-button>
          <el-button size="mini" type="primary" v-if="$store.getters.roles.indexOf('managesave1')>-1" @click="handlezhmm(row)">重置密码</el-button>
          <el-button size="mini" type="primary" v-if="$store.getters.roles.indexOf('managesave2')>-1"  @click="handleggmm(row)">重置谷歌</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <el-dialog
      :title="handleType === 1?'添加账号':'修改分组'"
      width="75%"
      v-dialogDrag
      :visible.sync="dialogVisible"
      round
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="create"
        label-position="left"
        label-width="auto"
      >
        <el-form-item v-if="handleType === 1" label="账号" prop="name">
          <el-input v-model="create.name"  placeholder="只支持英文和数字"  clearable />
        </el-form-item>
        <el-form-item  label="账号" v-if="this.handleType != 1">
          <span class="spanblock nameinput"  >{{create.name}}</span>
        </el-form-item>
        <el-form-item label="分组名称" prop="modelid">
          <el-select
            v-model="create.modelid"
            style="width: 100%"
            placeholder="选择分组"
          >
            <el-option
              v-for="item in options"
              :key="item.model_id"
              :label="item.modelname"
              :value="item.model_id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否脱敏">
          <el-radio-group v-model="create.is_view">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogEntry()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { managelist, moldelist, managesave, manageadd, managedel } from "@/api/systemAdminister";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";

export default {
  name: "roleAdminister",
  data() {
    let validatename = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("账号不能为空"));
      }else if (!/^[0-9a-zA-Z]+$/.test(value)) {
        callback(new Error("只能输入英文和数字"));
      }else if (!/^\w{4,16}$/.test(value)) {
        callback(new Error("长度必须在4~16位"));
      } else {
        callback();
      }
    };
    return {
      options: [],
      listLoading: false,
      listQuery: {
        pageNo: 1,
        pagesize: 10,
        name: undefined,
      },
      tableData: null,
      total: 0,
      handleType: 1, // 1添加,2修改
      dialogVisible: false, //控制添加对话框的显示和隐藏
      create: {
        name: undefined,
        modelid: undefined,
        pwd: '123abc',
        pwd2: '123abc',
        is_view: 0,
      },
      rules: {
        name: [{ validator: validatename, trigger: "blur" }],
        modelid: [
          { required: true, message: "请选择分组名称", trigger: "change" },
        ],
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
    moldelist({pageNo: 1, pagesize: 9999,}).then((res) => {
      this.options = res.data.list;
    });
  },

  methods: {
    //渲染table数据
    getList() {
      var that = this;
      //开始有加载中效果
      that.listLoading = true;
      // console.log(that.listQuery)
      managelist(that.listQuery).then((res) => {
        that.tableData = res.data.list;
        that.total = res.data.total;
        this.listLoading = false;
      });
    },
    //删除
    deleteClick(row, index) {
      this.$confirm('确认删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        managedel({upid: row.id}).then((res)=>{
          this.$notify({ title: '成功',message:res.msg,type: 'success'});
          if(this.tableData.length-1 === 0){
            //删除后已经不再有数据 页数减1
            this.listQuery.pageNo -= 1;
            this.listQuery.pageNo = this.listQuery.pageNo<1?1:this.listQuery.pageNo;
          }
          this.getList();
        })
      })
    },
    //修改
    updataClick(row) {
      this.handleType = 2
      this.dialogVisible = true;
      this.create.name = row.name;
      this.create.modelid = row.model_id;
      this.create.upid = row.id;
      this.create.is_view = row.is_view;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },

    //重置账号密码
    handlezhmm(row){
      this.$confirm('确认重置账号密码吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        managesave({
          upid: row.id,
          pwd: this.create.pwd,
          pwd2: this.create.pwd2,
          modelid: row.model_id,
          vkey: "",
        }).then(() => {
          this.$notify({
            title: "重置成功",
            type: "success",
            duration: 2000,
          });
          this.getList();
        });
      })
    },

    //重置谷歌密码
    handleggmm(row){
      this.create.upid = row.id;
      this.$confirm('确认重置谷歌密码吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        managesave({
          upid: row.id,
          vkey: "1",
          modelid: row.level,
        }).then(() => {
          this.$notify({
            title: "重置成功",
            type: "success",
            duration: 2000,
          });
          this.getList();
        });
      })
    },
    
    //添加账号
    createClick() {
      this.create.name = null;
      this.create.modelid = null;
      this.create.is_view = 0;
      this.handleType = 1
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    //添加对话框点击确定
    dialogEntry() {
      this.$refs["dataForm"].validate((valid) =>{
        if (valid) {
          if(this.handleType == 1){
            this.$confirm('确认添加吗?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              manageadd(this.create).then(() => {
                this.dialogVisible = false;
                this.$notify({
                  title: "添加成功",
                  type: "success",
                  duration: 2000,
                });
                this.getList();
              });
            })
          }else{
            managesave({
              name: this.create.name,
              modelid: this.create.modelid,
              upid: this.create.upid,
              is_view: this.create.is_view,
            }).then(() => {
              this.dialogVisible = false;
              this.$notify({
                title: "修改成功",
                type: "success",
                duration: 2000,
              });
              this.getList();
            });
          }
        }
      });
    },
    // 搜索事件
    handleFilter() {
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
</style>