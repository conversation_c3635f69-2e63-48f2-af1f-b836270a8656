<!--资产表-->
<template>
  <div class="overview-container">
    <div class="w_menu">
      <el-date-picker
        style="margin-left: 20px; margin-top: 20px"
        v-model="time"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
      ></el-date-picker>
      <el-button type="primary" @click="handleFilter" class="buttons" style="margin-top: 20px"
        >搜索</el-button
      >
    </div>
    <div class="w_c">
      <table>
        <tr>
          <th></th>
          <th>USDT</th>
        </tr>
        <tr>
          <th>资产账户</th>
          <th>{{Number(this.tableData.totalwallet || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr class="bg_color_one">
          <th>可用数量</th>
          <th>{{Number(this.tableData.walletbalance || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr class="bg_color_one">
          <th>冻结数量</th>
           <th>{{Number(this.tableData.withdrawlock || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr class="bg_color_one">
          <th>提币中数量</th>
           <th>{{Number(this.tableData.withdrawlock || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr>
          <th>交易账户</th>
          <th>{{Number(this.tableData.accountbalance || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr class="bg_color_one">
          <th>账户资产</th>
           <th>{{Number(this.tableData.accountbalance || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr class="bg_color_one">
          <th>可用余额</th>
           <th>{{Number(this.tableData.accountavailable || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr class="bg_color_one">
          <th>账户权益</th>
          <th>{{Number(this.tableData.accountprofit || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr class="bg_color_three">
          <th>用户_合计</th>
           <th>{{Number(this.tableData.usertotal || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr></tr>
        <tr>
          <th>平台钱包账户</th>
           <th>{{Number(this.tableData.platformbalance || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr class="bg_color_one">
          <th>负债资产</th>
          <th>{{Number(this.tableData.platformliabilities || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr class="bg_color_one">
          <th>平台合约账户</th>
          <th>{{Number(this.tableData.platformc2cmaccout || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr>
          <th>平台交易账户</th>
           <th>{{Number(this.tableData.platformaccout || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr class="bg_color_one">
          <th>已实现盈亏</th>
          <th>{{Number(this.tableData.platformaccoutporit || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr class="bg_color_three">
          <th>平台资产_合计</th>
          <th>{{Number(this.tableData.totalplatform || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr></tr>
        <tr class="bg_color_four">
          <th>平台+用户_合计</th>
          <th>{{Number(this.tableData.userPlat || 0).cutXiaoNum(6)}}</th>
        </tr>
        <tr></tr>
        
      </table>
    </div>
  </div>
</template>
<script>
import { getfinacewallet } from "@/api/platformFinance";
export default {
  name: "overView",
  data() {
    return {
      time: undefined,
      tableData: {},
    };
  },
  components: {},
  mounted() {
    this.time = this.getNowDate();
    this.getList(this.time);
  },
  computed: {},
  methods: {
    handleFilter(){
      if (this.time) {
        this.getList(this.time)
      } else {
        this.$notify({
          title: '提示',
          message: '请选择时间~',
          type: "warning",
          duration: 2000,
        })
      }
    },
    //数据
    getList(tdate) {
      getfinacewallet({ tdate }).then((res) => {
        if(res.data.length){
          this.tableData = res.data[0];
          this.tableData.usertotal = Number(Number(this.tableData.totalwallet).add(Number(this.tableData.accountbalance)));
          this.tableData.userPlat = Number(this.tableData.usertotal).add(Number(this.tableData.totalplatform));
          this.tableData.liquid = Number(this.tableData.userPlat).sub(Number(this.tableData.chainwallet));
        }else{
          this.tableData = {}
        }
      });
    },
    getNowDate() {
      var date = new Date();
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var day = date.getDate();
      if (month < 10) {
        month = "0" + month;
      }
      if (day < 10) {
        day = "0" + day;
      }
      return year + "-" + month + "-" + day;
    }
  },
};
</script>

<style lang="scss" scoped>
.buttons {
  margin-left: 30px;
}

.overview-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  .w_menu {
    width: 100%;
    flex-shrink: 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding-bottom: 10px;
  }
  .w_c {
    width: 100%;
    box-sizing: border-box;
    font-family: Arial;
    table {
      width: 95%;
      border: 1px solid #c0c4cc;
      border-collapse: collapse;
      margin-bottom: 15px;
      font-size: 14px;
      margin: 20px auto;
      tr {
        width: 100%;
        text-align: center;
        height: 35px;
        th {
          width: 14%;
          border: 1px solid #c0c4cc;
          font-size: 16px;
          height: 40px;
        }
        td {
          width: 25%;
          border: 1px solid #c0c4cc;
        }
      }
      .bg_color_one {
        background: rgb(245, 245, 255);
        font-weight: normal;
      }
      .bg_color_two {
        background: rgb(255, 254, 203);
      }
      .bg_color_three {
        background: rgb(255, 241, 178);
      }
      .bg_color_four {
        background: rgb(255, 251, 0);
      }
      .bg_color_five {
        background: rgb(0, 255, 234);
      }
      .bg_color_six {
        background: rgb(12, 28, 255);
        color: #fff;
      }
    }
  }
}
</style>
