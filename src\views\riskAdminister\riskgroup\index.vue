<template>
  <div class="lebeladminister-container">
    <div class="filter-container">
      <el-button
        v-if="$store.getters.roles.indexOf('setgrouplist') > -1"
        type="primary"
        style="margin-left: 30%; margin-bottom: 10px; float: right"
        @click="AddClick()"
        >新建组别</el-button
      >
    </div>

    <el-table
      v-loading="listLoading"
      :data="lebelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column
        type="index"
        label="编号"
        align="center"
        min-width="50"
      />
      <el-table-column
        label="组别名称"
        prop="group_name"
        align="center"
        min-width="100"
      />
      <el-table-column
        label="代理UID"
        prop="uids"
        align="center"
        min-width="78"
      />
      <el-table-column label="总入金区间" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_cash_in }} - {{ "不限" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总出金区间" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_cash_out }} - {{ "不限" }}<!-- row.max_cash_out --></span>
        </template>
      </el-table-column>
      <el-table-column label="净入金区间" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_net_cash }} - {{ "不限" }}<!-- row.max_net_cash --></span>
        </template>
      </el-table-column>
      <el-table-column label="平仓笔数区间" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_close }} - {{ "不限" }}<!-- row.max_close --></span>
        </template>
      </el-table-column>
      <el-table-column label="交易次数" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_trade_num }} - {{ "不限" }}<!-- row.max_trade_num --></span>
        </template>
      </el-table-column>
      <el-table-column label="PNL区间" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_pnl }} - {{ "不限" }}<!-- row.max_pnl --></span>
        </template>
      </el-table-column>
      <el-table-column label="手续费区间" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_commis }} - {{ "不限" }}<!-- row.max_commis --></span>
        </template>
      </el-table-column>
      <el-table-column label="资金费用区间" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_catp }} - {{ "不限" }}<!-- row.max_catp --></span>
        </template>
      </el-table-column>
      <el-table-column label="净PNL区间" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_net_pnl }} - {{ "不限" }}<!-- row.max_net_pnl --></span>
        </template>
      </el-table-column>
      <el-table-column label="盈利率区间" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_profit }} - {{ "不限" }}<!-- row.max_profit --></span>
        </template>
      </el-table-column>
      <el-table-column label="胜率区间" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_warn_profit }} - {{ "不限" }}<!-- row.max_warn_profit --></span>
        </template>
      </el-table-column>
      <el-table-column
        label="手续费占净入金比率区间"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.min_commis_profit }} - {{ "不限" }}<!-- row.max_commis_profit --></span>
        </template>
      </el-table-column>
      <el-table-column label="占用保证金区间" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_bond }} - {{ "不限" }}<!-- row.max_bond --></span>
        </template>
      </el-table-column>
      <el-table-column label="标签" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.lable_id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.status ? "生效中" : "已失效" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$store.getters.roles.indexOf('setgrouplist') > -1"
        label="操作"
        min-width="220"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-button size="mini" @click="handleEdit(row)">编辑</el-button>
          <el-button
            size="mini"
            :type="row.status ? 'info' : 'success'"
            @click="handleModifyStatus(row)"
            >{{ row.status ? "关闭" : "开启" }}
          </el-button>
          <!-- <el-button type="danger" size="mini" @click="handleDel(row)"
            >删除</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <el-dialog
      @close="closeConfigLabelDialog"
      :title="(this.handleDialogType == 'add' ? '添加' : '编辑')+'组别'"
      :visible.sync="addAndConfigDialogVisible"
      width="580px"
      v-dialogDrag
    >
      <el-form
        ref="editDialogForm"
        :rules="rules"
        :model="editDialogData"
        label-width="auto"
        label-position="left"
        size="mini"
        style="margin: 0 20px 0 10px"
      >
        <el-form-item label="组别名称" prop="group_name">
          <el-input v-model="editDialogData.group_name" placeholder="请输入内容"></el-input>
        </el-form-item>
        <el-form-item label="代理uid" prop="uids">
          <el-input
            type="textarea"
            v-model="editDialogData.uids"
            placeholder="可输入多个代理ID，“,”隔开"
          ></el-input>
        </el-form-item>
        <el-form-item label="客户总入金" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_cash_in"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_cash_in"
                placeholder="请输入内容"
                @input="()=>{editDialogData.min_cash_in=editDialogData.min_cash_in.replace(/[^\d^\.]/g,'')}"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_cash_in" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_cash_in"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="客户总出金" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_cash_out"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_cash_out"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_cash_out" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_cash_out"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="客户净入金" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_net_cash"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_net_cash"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_net_cash" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_net_cash"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="平仓笔数" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_close"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_close"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_close" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_close"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="交易次数" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_trade_num"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_trade_num"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_trade_num" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_trade_num"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="PNL" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_pnl"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_pnl"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_pnl" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_pnl"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="手续费" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_commis"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_commis"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_commis" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_commis"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="资金费用" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_catp"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_catp"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_catp" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_catp"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="净PNL" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_net_pnl"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_net_pnl"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_net_pnl" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_net_pnl"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="盈利率" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_profit"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_profit"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_profit" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_profit"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="胜率" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_warn_profit"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_warn_profit"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_warn_profit" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_warn_profit"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="手续费占净入金比" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_commis_profit"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_commis_profit"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_commis_profit" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_commis_profit"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="占用保证金" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_bond"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_bond"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_bond" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_bond"
                placeholder="请输入内容"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="标签" label-position="left" prop="lable_id">
          <div style="display: flex; align-items: center">
            <el-select
              v-model="editDialogData.lable_id"
              placeholder="请选择标签"
              style="width: 100%"
            >
              <el-option
                v-for="item in labelOptions"
                :key="item.labelid"
                :label="item.label_name"
                :value="item.labelid"
              ></el-option>
            </el-select>
            <i
              v-show="handleDialogType == 'add'"
              @click="
                () => {
                  addLableDialog = true;
                }
              "
              class="el-icon-circle-plus-outline"
              style="font-size: 24px; padding-left: 10px"
            ></i>
          </div>
        </el-form-item>
        <el-form-item label="优先级" label-position="left" prop="level">
          <el-select
            v-model="editDialogData.level"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in total"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="configLabelEntry()"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      @close="addLabelResetFields"
      class
      title="添加标签"
      width="50%"
      :visible.sync="addLableDialog"
      :close-on-click-modal="false"
    >
      <el-form
        :rules="rules"
        label-position="left"
        ref="addLabelForm"
        :model="addLabelForm"
        label-width="auto"
      >
        <el-form-item label="标签" prop="addLabelVal">
          <el-input v-model="addLabelForm.addLabelVal"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="addLableDialog = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="entryAddLabel()"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      @close="closexgmm"
      class
      title="输入谷歌二维码"
      width="350px"
      :visible.sync="checkvkeyDialog"
      :close-on-click-modal="false"
      @submit.native.prevent
    >
      <el-form
        :rules="rules"
        label-position="left"
        ref="ruleForm"
        :model="ruleForm"
        label-width="auto"
      >
        <el-form-item label="验证码" prop="yzmVal">
          <el-input v-model="ruleForm.yzmVal" maxlength="6"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="checkvkeyDialog = false"
          >取 消</el-button
        >
        <el-button size="mini" type="primary" @click="entrySendyzm()"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getgrouplist,
  addlabel,
  addgroupinfo,
  getupgroupstatus,
  upgroupinfo,
  dellablinfo,
} from "@/api/riskAdminister";
import { bcprocontractset, getlabel, commckeckvkey } from "@/api/user";

export default {
  name: "riskgroup",
  data() {
    //单笔最小下单量
    var min_order_volume_V = (rules, value, callback) => {
      if (
        this.editDialogData.max_order_volume &&
        value &&
        Number(value) > Number(this.editDialogData.max_order_volume)
      ) {
        return callback(new Error("单笔最小下单量不能大于单笔最大下单量!!!"));
      } else {
        callback();
      }
    };
    return {
      listLoading: false,
      total: 0,
      lebelList: [],    // 表格数组
      labelOptions: [], // 标签数组
      listQuery: {
        pageNo: 1,
        pagesize: 10,
      },
      addLabelForm: {
        addLabelVal: "",
      },
      editDialogData: {
        group_name: '',   // ` 组别名字,
        uids: '',   // ` 顶级代理uid, 
        min_cash_in: null,    // 最小入金,`
        max_cash_in: null,    //  ` '最大入金', `
        min_cash_out: null,   // ` 最小出金',
        max_cash_out: null,   // `'最大出金',`
        min_close: null,    // `'最小平仓', `
        max_close: null,    // ` '最大平仓', `
        min_trade_num: null,    // ` '最小交易次数',`
        max_trade_num: null,    // `'最大交易次数',`
        min_pnl: null,    // `'最小pnl',`
        max_pnl: null,    // ` '最大pnl',`
        min_commis: null,   // ` '最小手续费',`
        max_commis: null,   // `'最大手续费',`
        min_catp: null,   // ` '最小资金费用',`
        max_catp: null,   // `  '最大资金费用',`
        min_net_pnl: null,    // ` '净PNL',
        max_net_pnl: null,    // ` '净PNL',`
        min_net_cash: null,   // 净入金
        max_net_cash: null,   // 净入金
        min_profit: null,   // `  '盈利率',`
        max_profit: null,   // `  '盈利率', `
        min_warn_profit: null,    // ` '胜率', `
        max_warn_profit: null,    // `  '胜率',
        min_commis_profit: null,    // `'手续费占比',`
        max_commis_profit: null,    // ` '手续费占比',`
        min_bond: null,   // 占用保证金
        max_bond: null,   // 占用保证金
        lable_id: null,   // ` '标签ID',`
        level: null,    // `  '优先级',
      },
      ruleForm: {
        yzmVal: "", // 谷歌验证码
      },
      addLableDialog: false, // 控制google验证弹框显示
      checkvkeyDialog: false, // 控制google验证弹框显示
      addAndConfigDialogVisible: false, //控制添加标签对话框的显示和隐藏
      handleDialogType: "", //add: 添加 edit:编辑
      rules: {
        yzmVal: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        addLabelVal: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        group_name: [
          { required: true, message: "该输入框不能为空", trigger: "blur" },
        ],
        // lablname: [
        //   {  message: "请选择标签", trigger: "change" },
        // ],
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    getlabel({}).then((res) => {
      this.labelOptions = res.data;
    });
    this.getList();
  },

  methods: {
    feeInput(val) {
      this.editDialogData.fee = this.clearNoNumOfAlert(val);
    },
    //只能输入数字只能有一个小数点，小数点不能在开头，不能在结尾，第一位允许添加负号
    clearNoNumOfAlert(value) {
      //得到第一个字符是否为负号
      var t = value.charAt(0);
      //先把非数字的都替换掉，除了数字和.
      value = value.replace(/[^\d.]/g, "");
      //必须保证第一个为数字而不是.
      value = value.replace(/^\./g, "");
      //保证只有出现一个.而没有多个.
      value = value.replace(/\.{2,}/g, ".");
      //保证.只出现一次，而不能出现两次以上
      value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      //如果第一位是负号，则允许添加
      if (t == "-") {
        value = "-" + value;
      }
      return value;
    },
    //点击添加标签对话框里面确定按钮
    configLabelEntry() {
      this.$refs["editDialogForm"].validate((valid) => {
        if (valid) {
          this.checkvkeyDialog = true;
        }
      });
    },
    //点击添加标签出现弹框
    AddClick() {
      this.handleDialogType = "add";
      this.addAndConfigDialogVisible = true;
      this.$nextTick(() => {
        this.$refs["editDialogForm"].clearValidate();
      });
    },
    // 列表编辑按钮
    handleEdit(row) {
      let data = this.transferType(JSON.stringify(row), 'string')
      Object.assign(this.editDialogData, data, {
        lable_id: Number(data.lable_id)?Number(data.lable_id):''
      });
      this.addAndConfigDialogVisible = true;
      this.handleDialogType = "edit";
    },
    //状态开启和关闭
    handleModifyStatus(row) {
      this.$confirm(`确认${(row.status ? "关闭" : "开启")+row.group_name}？`)
        .then((_) => {
          let data = JSON.parse(JSON.stringify(row));
          // for (const key in data) {
          //   if (data.hasOwnProperty(key)) {
          //     const element = data[key];
          //     if(/^[0-9]+.?[0-9]*$/.test(element)){
          //       data[key] = Number(element)
          //       return false
          //     }
          //   }
          // }
          Object.assign(data, { status: data.status ? 0 : 1 });
          getupgroupstatus(data).then((res) => {
            this.$notify({
              title: "操作",
              message: "操作成功",
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        })
        .catch((_) => {});
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      getgrouplist(this.listQuery).then((response) => {
        this.lebelList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
        this.getLabelList()
      });
    },
    // 渲染标签label_name
    getLabelList() {
      this.lebelList.map((item,index)=>{
        this.labelOptions.map((item1,index1)=>{
          if(item.lable_id === item1.labelid) {
            item.lable_id = item1.label_name
            // console.log(item.lable_id)
          }
        })
      })
    },
    closeConfigLabelDialog() {
      this.addLabelForm.addLabelVal = "";
      this.$refs["editDialogForm"].resetFields();
    },
    addLabelResetFields() {
      this.$refs["addLabelForm"].resetFields();
    },
    entryAddLabel() {
      this.$refs["addLabelForm"].validate((valid) => {
        if (valid) {
          addlabel({
            lablname: this.addLabelForm.addLabelVal,
          }).then((res) => {
            this.addLableDialog = false;
            this.addLabelForm.addLabelVal = "";
            this.$notify({
              title: "成功",
              message: "操作成功",
              type: "success",
              duration: 2000,
            });
            getlabel({}).then((res) => {
              this.labelOptions = res.data;
            });
          });
        } else {
          return false;
        }
      });
    },
    closexgmm() {
      this.$refs["ruleForm"].resetFields();
    },
    entrySendyzm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          commckeckvkey({ code: this.ruleForm.yzmVal }).then((res) => {
            // 对象内字段value转换成Number
            let data = this.transferType(JSON.stringify(this.editDialogData), 'number')
            if (this.handleDialogType == "add") {
              addgroupinfo(data).then((res) => {
                this.ruleForm.yzmVal = "";
                this.closeConfigLabelDialog();
                this.checkvkeyDialog = false;
                this.addAndConfigDialogVisible = false;
                this.$notify({
                  title: "添加",
                  message: "操作成功",
                  type: "success",
                  duration: 2000,
                });
                this.getList();
              });
            } else {
              upgroupinfo(data).then((res) => {
                this.ruleForm.yzmVal = "";
                this.closeConfigLabelDialog();
                this.checkvkeyDialog = false;
                this.addAndConfigDialogVisible = false;
                this.$notify({
                  title: "修改",
                  message: "操作成功",
                  type: "success",
                  duration: 2000,
                });
                this.getList();
              });
            }
          });
        } else {
          return false;
        }
      });
    },
    transferType(data, toType){
      let obj = {}
      Object.assign(obj,JSON.parse(data))
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const element = obj[key];
          // 排除非转换Number的
          if (
            ["group_name","uids","manage","creat_time","lable_id"].indexOf(key) ==
            -1
          ) {
            if(toType == 'number'){
              obj[key] = Number(element) || 0;
            }else{
              obj[key] = element+'' || '0';
            }
          } else {
            if (!element) {
              obj[key] = toType == 'number' ? 0 : '0';
            }
          }
        }
      }
      return obj
    }
  },
};
</script>
<style lang="scss" scoped>
</style>