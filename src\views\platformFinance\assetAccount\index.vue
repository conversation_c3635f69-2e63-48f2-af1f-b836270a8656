<template>
  <div class="assetaccount-container">
    <div class="history-record">
      <el-button style="float: right" v-if="$store.getters.roles.indexOf('platwalletdaillist')>-1" plain @click="historyClick()">历史记录</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="assetAccount"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="币种" prop="coinname" align="center" min-width="80"> </el-table-column>
      <el-table-column label="可用数量" prop="amount" align="center" min-width="110px">
        <template slot-scope="{ row }">
          <span>{{Number(row.amount)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="冻结数量" prop="amountlock" align="center" min-width="95px"> </el-table-column>
      <el-table-column label="平台负债" prop="assetaward" align="center" min-width="95px"> </el-table-column>
      <el-table-column label="操作" align="center" min-width="120px">
        <template slot-scope="{ row }">
          <el-button size="mini" type="primary" @click="handleCharge(row)">充币</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!--充币弹框-->
    <el-dialog
      title="充币"
      :visible.sync="dialogVisible_cb"
      width="50%"
      :close-on-click-modal="false"
      @open="init()"
      @close="()=>{dialogVisible_cb = false}"
    >
      <div class="dialogc">
        <div>
          <span>充币地址：</span>
          <span ref="copyText" id="copyText">{{this.dialogData?this.dialogData.address:''}}</span>
          <el-button
            style="padding-left:10px;"
            v-clipboard:copy="this.dialogData?this.dialogData.address:''"
            v-clipboard:success="clipboardSuccess"
            type="text"
          >复制</el-button>
        </div>
        <canvas ref="ewm"></canvas>
        <span>最小充值金额 {{(dialogData?dialogData.mixcharge:'')+(dialogData?dialogData.coinname:'').toUpperCase()}}</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="dialogcbOkClick">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { platwalletlist } from "@/api/platformFinance";
import clipboard from '@/directive/clipboard/index.js'
let QRCode = require("qrcode");
export default {
  name: "assetAccount",
  data() {
    return {
      //表格加载中效果
      listLoading: false,
      assetAccount: null,
      dialogVisible_cb: false,
      dialogData:null
    };
  },

  components: {},

  computed: {},
  directives: {
    clipboard
  },
  mounted() {
    this.getList();
  },

  methods: {
    handleCharge(v){
      this.dialogData = v;
      this.dialogVisible_cb = true
    },
    dialogcbOkClick() {
      this.dialogVisible_cb = false;
    },

    //  点击历史记录
    historyClick() {
      this.$router.push({
        path: "/platformFinance/overView/assetHistoryRecord",
      });
    },
    //数据
    getList() {
      this.listLoading = true;
      platwalletlist({}).then((res) => {
        this.assetAccount = res.data;
        this.listLoading = false;
      });
    },
    clipboardSuccess() {
      this.$notify({
        title: '复制',
        message: '复制成功',
        type: 'success',
        duration: 2000
      })
    },
    init() {
      this.$nextTick(function() {
        this.createQrc(this.dialogData.address); //"otpauth://totp/sugar?secret="+
      });
    },
    createQrc(val) {
      QRCode.toCanvas(this.$refs.ewm, val, error => {
        if (error) {
          console.log(error);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.assetaccount-container{
  .history-record{
    height: 20px;
    margin-top: 10px;
  }
  .dialogc {
    padding-top: 22px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}
</style>