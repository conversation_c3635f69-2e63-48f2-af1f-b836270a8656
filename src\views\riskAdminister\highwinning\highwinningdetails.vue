<template>
  <div class="userlistdetail-container">
    <el-row
      :gutter="6"
      style="border: 1px solid #f0f2f5; width: 99.5%; margin: 20px auto"
      class="top_info"
    >
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8" style="padding: 0">
        <div class="grid-content">
          <span class="grid-content-left">UID:</span>
          <span class="grid-content-right">{{ ListDetail.user_id }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">顶级代理ID:</span>
          <span class="grid-content-right">{{
            ListDetail.topagent || "--"
          }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">上级ID:</span>
          <span class="grid-content-right">{{
            ListDetail.paregant || "--"
          }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">上级ID:</span>
          <span class="grid-content-right">{{
            ListDetail.parename || "--"
          }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">交易次数:</span>
          <span class="grid-content-right">{{ ListDetail.trader_num }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">平仓次数:</span>
          <span class="grid-content-right">{{
            ListDetail.win_trading_close
          }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">历史胜率:</span>
          <span class="grid-content-right">{{ ListDetail.win_rate }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">PNL:</span>
          <span class="grid-content-right">{{ ListDetail.win_pnl }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">最近24H交易次数:</span>
          <span class="grid-content-right">{{
            ListDetail.within24_tradenum
          }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">平均交易周期:</span>
          <span class="grid-content-right">{{
            ListDetail.average_tradeperiod
          }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">最大单笔盈利:</span>
          <span class="grid-content-right">{{
            ListDetail.max_profit_per
          }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">当前持仓价值:</span>
          <span class="grid-content-right">{{
            ListDetail.position_value
          }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">占用保证金:</span>
          <span class="grid-content-right">{{ ListDetail.margin_sum }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">净入金:</span>
          <span class="grid-content-right">{{ ListDetail.pure_deposit }}</span>
        </div>
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8" style="padding: 0">
        <div class="grid-content">
          <span class="grid-content-left">用户名:</span>
          <span class="grid-content-right">{{
            ListDetail.user_name || "--"
          }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">顶级代理昵称:</span>
          <span class="grid-content-right">{{
            ListDetail.petname || "--"
          }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">上级用户名:</span>
          <span class="grid-content-right">{{
            ListDetail.parename || "--"
          }}</span>
        </div>
        <div class="grid-content"></div>
        <div class="grid-content">
          <span class="grid-content-left">开仓次数:</span>
          <span class="grid-content-right">{{ ListDetail.open_num }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">盈利次数:</span>
          <span class="grid-content-right">{{ ListDetail.win_close }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">净PNL:</span>
          <span class="grid-content-right">{{ ListDetail.pure_pnl }}</span>
        </div>
        <div class="grid-content"></div>
        <div class="grid-content">
          <span class="grid-content-left">最近24H净PNL:</span>
          <span class="grid-content-right">{{ ListDetail.within24_pnl }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">最大单笔亏损:</span>
          <span class="grid-content-right">{{ ListDetail.max_loss_per }}</span>
        </div>
        <div class="grid-content"></div>
        <div class="grid-content">
          <span class="grid-content-left">浮动PNL:</span>
          <span class="grid-content-right">{{ ListDetail.float_profit }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">风险度:</span>
          <span class="grid-content-right">{{ ListDetail.risk }}</span>
        </div>
        <div class="grid-content"></div>
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8"> </el-col>
    </el-row>

    <div style="border: 1px solid #f0f2f5">
      <div class="grid-content">
        <h3 class="grid-content-left">资产</h3>
      </div>
      <el-row :gutter="6" style="margin: 30px 20px; border: 1px solid #f0f2f5">
        <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
          <div class="grid-content">
            <span class="font">钱包账户资产(USDT):</span>
          </div>
          <div class="grid-content" style="border-bottom: 1px solid #f0f2f5">
            <span>{{ ListDetail.wallettotal }}</span>
          </div>
          <el-row>
            <el-col :span="12">
              <div class="grid-content">
                <span class="font">可用余额</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.walletbalance }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-content">
                <span class="font">冻结数量</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.walletlock }}</span>
              </div>
            </el-col>
          </el-row>
        </el-col>
        <el-col
          :xs="8"
          :sm="8"
          :md="8"
          :lg="8"
          :xl="8"
          style="border-left: 1px solid #f0f2f5"
        >
          <div class="grid-content">
            <span class="font">合约账户资产(USDT):</span>
          </div>
          <div class="grid-content" style="border-bottom: 1px solid #f0f2f5">
            <span>{{ ListDetail.accbalance }}</span>
          </div>
          <el-row>
            <el-col :span="12">
              <div class="grid-content">
                <span class="font">账户权益</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.accequity }}</span>
              </div>
              <div class="grid-content">
                <span class="font">冻结保证金</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.accbond }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-content">
                <span class="font">可用余额</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.accavailable }}</span>
              </div>
              <div class="grid-content">
                <span class="font">未实现盈亏</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.accprofit }}</span>
              </div>
            </el-col>
          </el-row>
        </el-col>
        <el-col
          :xs="8"
          :sm="8"
          :md="8"
          :lg="8"
          :xl="8"
          style="border-left: 1px solid #f0f2f5"
        >
          <div class="grid-content">
            <span class="font">跟单账户资产(USDT):</span>
          </div>
          <div class="grid-content" style="border-bottom: 1px solid #f0f2f5">
            <span>{{ ListDetail.followbalance }}</span>
          </div>
          <el-row>
            <el-col :span="12">
              <div class="grid-content">
                <span class="font">账户权益</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.followequity }}</span>
              </div>
              <div class="grid-content">
                <span class="font">冻结保证金</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.followbond }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-content">
                <span class="font">可用余额</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.followavailable }}</span>
              </div>
              <div class="grid-content">
                <span class="font">未实现盈亏</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.followprofit }}</span>
              </div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
const userTypeOptions = {
  1: "顶级代理",
  2: "代理",
  3: "普通用户",
  4: "代理直推用户",
};
import { userinfo, userpayments, resetuserphone } from "@/api/userQuery";
export default {
  name: "highwinningdetails",
  data() {
    return {
      ListDetail: {
        user_id: "--",
      },
      payInfo: [],
      //用户类型
      userTypeOptions,
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getDetails();
    userpayments({ user_id: JSON.parse(this.$route.query.id) }).then((data) => {
      this.payInfo = data.data;
    });
  },

  methods: {
    getDetails() {
      userinfo({ user_id: JSON.parse(this.$route.query.id) }).then((data) => {
        Object.assign(
          this.ListDetail,
          data.data,
          JSON.parse(this.$route.query.details)
        );
      });
    },
    reset(type) {
      let obj = {
        1: "手机号",
        2: "邮箱",
        3: "资金密码",
        4: "谷歌验证码",
      };
      this.$confirm("确认重置当前用户的" + obj[type], "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          resetuserphone({
            uid: JSON.parse(this.$route.query.id) + "",
            stype: type,
          }).then((data) => {
            this.$notify({
              title: "成功",
              message: "操作成功",
              type: "success",
              duration: 2000,
            });
            this.getDetails();
          });
        })
        .catch(() => {
          // this.$notify({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 99.7%;
  height: 100px;
  // background: firebrick;
  border: 1px solid #f0f2f5;
  margin: 20px auto;
  display: flex;
  flex-wrap: wrap;
  // flex-direction: column;
  align-items: center;
  // justify-content: space-around;

  .wc_1-one {
    width: 45%;
    // background: firebrick;
    padding-left: 10%;
    box-sizing: border-box;
  }
}
.top_info {
  .grid-content {
    border-bottom: 1px solid #f0f2f5;
    border-right: 1px solid #f0f2f5;
  }
}
.grid-content {
  min-height: 40px;
  display: flex;
  padding-left: 15px;
  align-items: center;

  .grid-content-right {
    word-wrap: break-word;
    word-break: normal;
  }
  .grid-content-left {
    width: 150px;
    font-size: 14px;
  }
}
.font {
  font-size: 14px;
  color: #999;
}
</style>