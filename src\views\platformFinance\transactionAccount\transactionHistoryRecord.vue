<template>
  <div class="historyrecord-container">
    <el-table
      v-loading="listLoading"
      :data="assetHistoryList"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
      size="mini"
    >
      <el-table-column label="时间" prop="createat" align="center" min-width="90px" >
      </el-table-column>
      <el-table-column label="划转UID" prop="userid" align="center" min-width="60" >
      </el-table-column>
      <el-table-column label="币种" prop="coinname" align="center" min-width="100px" />
      <el-table-column label="类型" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{optypeObj[row.optype]}}</span>
        </template>
      </el-table-column>
      <el-table-column label="变化数量" prop="amount" min-width="90px" align="center"/>
      <el-table-column label="剩余可用余额" prop="amountafter" min-width="90px" align="center">
        <template slot-scope="{row}">
          <span>{{row.amountafter || '--'}}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="剩余已实现盈亏" prop="amountafter" min-width="90px" align="center"/> -->

      <el-table-column label="操作人" align="center" min-width="120px">
        <template slot-scope="{ row }">
          <span>{{ row.opusername || '系统自动'}}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { plataccountdail } from "@/api/platformFinance";
export default {
  name: "transactionHistoryRecord",
  data() {
    return {
      //表格加载中效果
      listLoading: false,
      assetHistoryList: null,
      total: 0,
      //页数页码
      listQuery: {
        star: "", //开始
        end: "", //结束
        pageNo: 1,
        pagesize: 10,
      },
      optypeObj: {
        1: "交易手续费",
        4: "提币手续费",
        8: "盈亏收入",
        9: "资金费用",
        100: "平台账户充币",
        200: "平台账户提币",
        300: "转入",
        301: "转入到交易账户",
        302: "转入到资产账户",
        310: "转出",
        311: "转出到交易账户",
        312: "转出到资产账户",
        320: "平台提币手续费",
        330: "邀请佣金奖励",
        331: "代理佣金奖励",
        332: "空投",
        333: "C2C结算",
        334: "C2C其他用途",
        340: "模拟盘补领资产",
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      plataccountdail(this.listQuery).then((response) => {
        this.assetHistoryList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>

</style>