<template>
  <div class="highwinning-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.user_id"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.top_agent_id"
        size="mini"
        placeholder="顶级代理ID"
        style="width: 130px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.petname"
        size="mini"
        placeholder="顶级代理昵称"
        style="width: 130px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.pareid"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.parename"
        size="mini"
        placeholder="上级代理昵称"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">净PNL≥</span>
       <el-input
        v-model="listQuery.pure_pnl"
        size="mini"
        placeholder="请输入"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">PNL≥</span>
       <el-input
        v-model="listQuery.pnl"
        size="mini"
        placeholder="请输入"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">浮动PNL≥</span>
       <el-input
        v-model="listQuery.float_pnl"
        size="mini"
        placeholder="请输入"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px;margin-right:5px;">时间</span>
      <el-date-picker
        v-model="listQuery.date"
        size="mini"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期">
      </el-date-picker>
      <!-- <el-date-picker
        style="width: 220px; margin-top: 10px; margin-left: 5px"
        class="picker"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change='dataTimeChange'
      > 
      </el-date-picker>-->
      <el-select
        size="mini"
        v-model="listQuery.exclude_condition"
        placeholder="无剔除用户"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in dayOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-button
        style="margin-left: 20px; margin-top: 5px"
        class="filter-item"
        size="mini"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('highwinningexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        导出
      </el-button>
      
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="user_id" align="center" min-width="78"> </el-table-column>
      <!-- <el-table-column label="用户名" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="交易次数" prop="trader_num" align="center" min-width="60"> </el-table-column>
      <!-- <el-table-column label="开仓次数" prop="open_num" align="center" min-width="60"> </el-table-column>
      <el-table-column label="平仓次数" prop="win_trading_close" align="center" min-width="60"> </el-table-column> -->
      <el-table-column label="盈利次数" prop="win_close" align="center" min-width="60"> </el-table-column>
      <el-table-column label="历史胜率" prop="win_rate" align="center" min-width="60">
        <template slot-scope="{row}">
          <span>{{row.win_rate + '%'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="净PNL" prop="pure_pnl" align="center" min-width="90"> </el-table-column>
      <el-table-column label="PNL" prop="win_pnl" align="center" min-width="90"> </el-table-column>
      <el-table-column label="最近24H交易次数" prop="within24_tradenum" align="center" min-width="60"> </el-table-column>
      <el-table-column label="最近24H净PNL" prop="within24_pnl" align="center" min-width="60"> </el-table-column>
      <el-table-column label="平均交易周期" prop="average_tradeperiod" align="center" min-width="60"> </el-table-column>
      <el-table-column label="最大单笔亏损" prop="max_loss_per" align="center" min-width="90"> </el-table-column>
      <el-table-column label="最大单笔盈利" prop="max_profit_per" align="center" min-width="90"> </el-table-column>
      <el-table-column label="当前持仓价值" prop="position_value" align="center" min-width="90"> </el-table-column>
      <el-table-column label="浮动PNL" prop="float_profit" align="center" min-width="90"> </el-table-column>
      <!-- <el-table-column label="占用保证金" prop="margin_sum" align="center" min-width="90"> </el-table-column> -->
      <el-table-column label="风险度" prop="risk" align="center" min-width="90"> </el-table-column>
      <!-- <el-table-column label="可用余额" prop="available" align="center" min-width="90"> </el-table-column>
      <el-table-column label="资产账户余额" prop="balance" align="center" min-width="90"> </el-table-column>
      <el-table-column label="净入金" prop="pure_deposit" align="center" min-width="90"> </el-table-column> -->
      <el-table-column label="最近登录时间" prop="last_login_time" align="center" width="75"> </el-table-column>
      <el-table-column label="操作" align="center" width="100">
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            type="primary"
            @click="detailClick(row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200,100,50,20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//引入封装接口
import {
  highwinning, highwinningexport,
} from "@/api/riskAdminister";

export default {
  name: "highwinning",
  data() {
    
    return {
      total: 0,
      tableData: [],
      listLoading: true,
      filterTime: [],
      listQuery: {
        user_id: "",
        top_agent_id: "",
        petname: "",
        pareid: "",
        parename: "",
        pure_pnl: "", // 净pnl
        pnl: "", // PNL
        float_pnl: "",// 
        exclude_condition: "", 
        date: "",
        pageNo: 1,
        pagesize: 20,
      },
      dayOptions: [
        { key: 3, name: "3日无交易" },
        { key: 5, name: "5日无交易" },
        { key: 7, name: "7日无交易" },
        { key: 15, name: "15日无交易" },
      ], 
      exportLoading: false, //导出加载中效果
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    //点击查看跳转详情页
    detailClick(row){
      // var user_id = JSON.parse(row.user_id)
      this.$router.push({
        path: "/riskAdminister/highwinningdetails",
        query: { 
          id:JSON.parse(row.user_id),
          details: JSON.stringify(row)
        },
      });
    },
    // 回车搜索事件
    handleFilter(){
      this.listQuery.pageNo = 1
      this.getList();
    },
    //  table列表数据
    getList() {
      //开始有加载中效果
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      data.pure_pnl = this.listQuery.pure_pnl?Number(this.listQuery.pure_pnl):0
      data.pnl = this.listQuery.pnl?Number(this.listQuery.pnl):0
      data.float_pnl = this.listQuery.float_pnl?Number(this.listQuery.float_pnl):0
      data.pareid = this.listQuery.pareid?JSON.parse(this.listQuery.pareid):0
      data.top_agent_id = this.listQuery.top_agent_id?JSON.parse(this.listQuery.top_agent_id):0
      data.exclude_condition = this.listQuery.exclude_condition?parseInt(this.listQuery.exclude_condition):0
      highwinning(data).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    dataTimeChange(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val && val[1] || '';
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      data.pure_pnl = this.listQuery.pure_pnl?Number(this.listQuery.pure_pnl):0
      data.pnl = this.listQuery.pnl?Number(this.listQuery.pnl):0
      data.float_pnl = this.listQuery.float_pnl?Number(this.listQuery.float_pnl):0
      data.pareid = this.listQuery.pareid?JSON.parse(this.listQuery.pareid):0
      data.top_agent_id = this.listQuery.top_agent_id?JSON.parse(this.listQuery.top_agent_id):0
      data.exclude_condition = this.listQuery.exclude_condition?parseInt(this.listQuery.exclude_condition):0
      highwinningexport(data).then((res) => {
        if(res.ret == 0){
          this.$notify.success({title:'操作成功',message:"请到‘交易查询/导出下载列表’进行下载"})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 0 10px;
 
  // justify-content: space-around;
  .wc_1-one {
    width: 45%;
  }
  .idPhotoImg_wrap ::v-deep .el-image__inner{
    width: auto;
   
  }
  .idPhotoImg_wrap ::v-deep .el-image__error{
    min-width: 100px;
  }
}
</style>