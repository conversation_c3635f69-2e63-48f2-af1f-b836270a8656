<template>
  <div class="administer-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID/昵称"
        style="width: 130px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.stype"
        placeholder="状态"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        @clear = "stypeclear()"
      >
        <el-option
          v-for="item in stypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin-right: 5px; font-size: 12px">申请时间</span>
      <el-date-picker
        style="width: 220px;margin-right: 20px; margin-top: 10px"
        v-model="filterTime1"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change="filterTimeTransform1"
      />
      <span style="margin-right: 5px; font-size: 12px">审核时间</span>
      <el-date-picker
        style="width: 220px;margin-right: 20px; margin-top: 10px"
        v-model="filterTime2"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change="filterTimeTransform2"
      />
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="administerList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="userid" align="center" min-width="78"/>
      <el-table-column label="用户名" prop="user_name" align="center" min-width="95" />
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78">
         <template slot-scope="{row}">
          <span>{{row.top_agent_id || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="78">
       <template slot-scope="{row}">
          <span>{{row.pareid || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单编号" prop="billid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ parseFloat(row.billid).toPrecision(18)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="币种" prop="currencyname" align="center" min-width="80px"/>
      <el-table-column label="提现数量" prop="amount" min-width="90px" align="center"/>
      <el-table-column label="转出地址" prop="toaddr" min-width="200px" align="center"/>
      <el-table-column
        label="状态"
        prop="netcash"
        min-width="110px"
        align="center"
      >
        <template slot-scope="{ row }">
          <span>{{
            (row.status && stypeOptions.find((v)=>(v.key == row.status)) && stypeOptions.find((v) => v.key == row.status).name) ||
            "--"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发起时间" prop="createdtime" width="75" align="center"/>
      <el-table-column label="处理时间" prop="platfom_time" width="75" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.status > 1 && row.platfom_time || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="270px" v-if="$store.getters.roles.indexOf('getwithdrawlist')>-1 || $store.getters.roles.indexOf('withdrawcheck')>-1">
        <template slot-scope="{ row }">
          <el-button type="success" v-if="row.status == 1 && $store.getters.roles.indexOf('withdrawcheck')>-1" size="mini" @click="handleUpdate(row,1,'first')">初审通过</el-button>
          <el-button  type="danger" v-if="row.status ==1 && $store.getters.roles.indexOf('withdrawcheck')>-1" size="mini" @click="handleUpdate(row,0,'first')">初审拒绝</el-button>
          <el-button v-if="row.status == 7 && $store.getters.roles.indexOf('withdrawcheck')>-1" type="success" size="mini" @click="handleUpdate(row,1,'second')">复审通过</el-button>
          <el-button  type="danger" v-if="row.status == 7 && $store.getters.roles.indexOf('withdrawcheck')>-1" size="mini" @click="handleUpdate(row,0,'second')">复审拒绝</el-button>
          <el-button type="primary" size="mini" v-if="$store.getters.roles.indexOf('getwithdrawlist')>-1" @click="handleUpdate(row,2)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作人" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.platfom_mange || "--" }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="备注" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{ row.remarks || "--" }}</span>
        </template>
      </el-table-column> -->
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />

    <!-- 点击查看 -->
    <el-dialog
      :title="dialogStatus == 1 ? (this.reviewType == 'first'?'初审':'复审')+'通过' : dialogStatus == 0 ?  (this.reviewType == 'first'?'初审':'复审')+'拒绝' : '查看'"
      :visible.sync="UpdataDialogVisible"
      width="75%"
      v-dialogDrag
    >
      <div style="width: 100%;text-align: center;">
        <div class="wc_1">
          <div class="wc_1-one">UID:</div>
          <div>{{ updata.userid }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">币种:</div>
          <div>{{ updata.currencyname }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">申请数量:</div>
          <div>{{ updata.amount }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">到账数量:</div>
          <div>{{ Number(updata.amount).sub(updata.commission) }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">手续费:</div>
          <div>{{ updata.commission }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">转出地址:</div>
          <div >{{ updata.toaddr}}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">申请时间:</div>
          <div>
            {{ updata.createdtime }}
          </div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">处理时间:</div>
          <div>{{ updata.status > 1 && updata.platfom_time || "--" }}</div>
        </div>
        <div v-if="dialogStatus == 2" class="wc_1">
          <div class="wc_1-one">状态:</div>
          <div>
            <span>{{(updata.status && stypeOptions.find((v) => v.key == updata.status).name) || "--"}}</span>
          </div>
        </div>
        <div v-show="this.reviewType">
          <el-table
            v-loading="checkwithdrawlistLoading"
            :data="checkwithdrawlist"
            border
            fit
            highlight-current-row
            style="width: 100%; margin-top: 30px"
            :header-cell-style="{ background: '#F0F8FF' }"
            size="mini"
          >
            <el-table-column label="累计充币" prop="inamout" align="center" min-width="110px">
              <template slot-scope="{ row }">
                <span>{{row.inamout ? '+' : ''}}</span>
                <span>{{row.inamout}}</span>
              </template>
            </el-table-column>
            <el-table-column label="累计法币买入" prop="inlegal" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.inlegal ? '+' : ''}}</span>
                <span>{{row.inlegal}}</span>
              </template>
            </el-table-column>
            <el-table-column label="累计奖励" prop="inreward" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.inreward ? '+' : ''}}</span>
                <span>{{row.inreward}}</span>
              </template>
            </el-table-column>
            <el-table-column label="累计空投" prop="inairdrop" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.inairdrop ? '+' : ''}}</span>
                <span>{{row.inairdrop}}</span>
              </template>
            </el-table-column>
            <el-table-column label="累计平仓PNL" prop="totalprofit" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.totalprofit && (row.totalprofit>0?'+':'')}}</span>
                <span>{{row.totalprofit}}</span>
              </template>
            </el-table-column>
            <el-table-column label="累计手续费" prop="commission" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.commission ? '-' : ''}}</span>
                <span>{{row.commission}}</span>
              </template>
            </el-table-column>
            <el-table-column label="累计资金费用" prop="incapital" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.incapital && (row.incapital>0?'+':'')}}</span>
                <span>{{row.incapital}}</span>
              </template>
            </el-table-column>
            <el-table-column label="累计提币" prop="outamout" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.outamout ? '-' : ''}}</span>
                <span>{{row.outamout}}</span>
              </template>
            </el-table-column>
            <el-table-column label="累计法币卖出" prop="outlegal" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.outlegal ? '-' : ''}}</span>
                <span>{{row.outlegal}}</span>
              </template>
            </el-table-column>
            <el-table-column label="预计可提币" prop="createdtime" min-width="110px" align="center">
              <template>
                <span :style="{'color':computedCan<0?'red':'green'}">{{computedCan}}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-show="this.reviewType">
          <el-table
            v-loading="userloglistLoading"
            :data="userloglist"
            border
            fit
            highlight-current-row
            style="width: 100%; margin-top: 30px"
            :header-cell-style="{ background: '#F0F8FF' }"
            size="mini"
          >
            <el-table-column label="操作类型" min-width="90px" align="center">
              <template slot-scope="{ row }">
                <span>{{typeObj[row.op_type]}}</span>
              </template>
            </el-table-column>
            <el-table-column label="IP地址" prop="ip_address" align="center" min-width="130"> </el-table-column>
            <el-table-column label="设备" prop="device" align="center" min-width="120"> </el-table-column>
            <el-table-column label="设备ID" prop="device_id" align="center" min-width="120"> </el-table-column>
            <el-table-column label="操作系统" prop="price" align="center" min-width="90px">
              <template slot-scope="{row}">
                <span>{{os_typeObj[row.os_type]}}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作时间" prop="created_time" align="center" min-width="100px"> </el-table-column>
          </el-table>
        </div>
        <!-- <div v-else class="wc_1" style="margin-top: 20px">
          <div class="wc_1-one">备注(选填):</div>
          <div style="width:60%">
            <el-input
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
              v-model="updata.remarks"
            >
            </el-input>
          </div>
        </div> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <span  v-if="dialogStatus != 2" style="margin-left: 50px; margin-right: 10px;  color: #ffbe47; font-size: 14px"
          >确认信息无误后请谨慎操作</span
        >
        <el-button @click="UpdataDialogVisible = false">{{dialogStatus != 2?'取消':'关闭'}}</el-button>
        <el-button v-if="dialogStatus != 2" type="primary" @click="dialogEntry(updata)">{{dialogStatus?'确认通过':'确认拒绝'}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getwithdrawlist, withdrawcheck ,firstwithdrawcheck, getcheckwithdrawlist, getuserloglis} from "@/api/fundQuery";

export default {
  name: "withdrawaladminister",
  data() {
    return {
      listLoading: false,
      total: 0,
      administerList: null,
      filterTime1: [],
      filterTime2: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        substar: "", //提交开始
        subend: "", //提交结束
        checkstar: "", //审核开始
        checkend: "", //审核结束
        stype: null, // 1：待审核 2：已到账  5平台拒绝，6 平台审核通过
        pageNo: 1,
        pagesize: 10,
      },
      dialogStatus: null,
      UpdataDialogVisible: false,//控制查看对话框的显示与隐藏
      updata: {
        remarks: ''
      },
      reviewType:null,
      stypeOptions: [
        { key: 1, name:'待审核' },
        { key: 2, name:'已到账' },
        { key: 3, name:'链上提币已提交' },
        { key: 4, name:'链上提币拒绝' },
        { key: 5, name:'平台拒绝' },
        { key: 6, name:'平台审核通过' },
        { key: 7, name:'初审通过'},
        { key: 8, name:'初审拒绝'}
      ],
      checkwithdrawlist: [],
      checkwithdrawlistLoading: false,
      userloglist: [],
      userloglistLoading: false,
      typeObj: {
        0:"登录",
        1:"注册",
        2:"找回登录密码",
        3:"设置登录密码",
        4:"修改登录密码",
        5:"修改资金密码",
        6:"修改手机号",
        7:"修改邮箱",
        8:"提现申请",
        9: "设置资金密码",
        10: "设置谷歌验证器",
        11: "修改谷歌验证器",
        12: "设置手机号",
        13: "设置邮箱",
        14: "KYC1申请",
        15: "KYC2申请",
        16: "人工KYC申请",
      },
      os_typeObj:{
        1: "android",
        2: "iOS",
        3: "WEB",
        4: "H5",
        5: "open_api",
        6: "系统自动",
      },
    };
  },

  components: {},

  computed: {
    computedCan(){
      if(this.checkwithdrawlist.length>0){
        let data = this.checkwithdrawlist[0]
        let plus = Number(Number(Number(Number(data.inamout).add(data.inlegal)).add(data.inreward)).add(data.inairdrop)).add(data.incapital)
        let minus = Number(Number((Math.abs(data.commission))).add(Math.abs(data.outamout))).add(Math.abs(data.outlegal))
        return Number(Number(plus).sub(minus)).add(data.totalprofit)
      }else{
        return '--'
      }
    },
  },

  mounted() {
    this.getList();
  },

  methods: {
    stypeclear(){
      this.listQuery.stype = null
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      getwithdrawlist(this.listQuery).then((res) => {
        this.administerList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },

    //查看
    handleUpdate(row,status,type) {
      // this.updata.billid = row.billid
      // this.updata.remarks = row.remarks
      this.updata = Object.assign({}, row);
      this.UpdataDialogVisible = true;
      this.dialogStatus = status
      this.reviewType = type        
      if(!type) return
      this.checkwithdrawlistLoading = true
      getcheckwithdrawlist({user_id: row.userid}).then((res) => {
        this.checkwithdrawlist = []
        this.checkwithdrawlist.push(res.data);
        this.checkwithdrawlistLoading = false;
      });
      this.userloglistLoading = true;
      getuserloglis({user_id: row.userid}).then((res) => {
        this.userloglist = res.data;
        this.userloglistLoading = false;
      });
    },

    //审核通过或拒绝
    dialogEntry() {
      // 初审
      if(this.reviewType == 'first'){
        firstwithdrawcheck({
          id:this.updata.billid,
          pass: this.dialogStatus == 0 ? false : true,//true 通过 false 驳回
        }).then(res=>{
          this.UpdataDialogVisible = false
          this.$notify({
            title: "操作成功",
            type: "success",
            duration: 2000,
          });
          this.getList()
        })
      }
      // 复审
      if(this.reviewType == 'second'){
        withdrawcheck({
          id: this.updata.billid,//数据id
          pass: this.dialogStatus == 0 ? false : true,//true 通过 false 驳回
          content: this.updata.remarks//备注
        }).then(() => {
          this.UpdataDialogVisible = false;
          this.updata.remarks = ''
          this.$notify({
            title: "操作成功",
            type: "success",
            duration: 2000,
          });
          this.getList()
        });
      }
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform1(val) {
      this.listQuery.substar = (val && val[0]) || "";
      this.listQuery.subend = (val && val[1] + " 23:59:59") || "";
    },
    filterTimeTransform2(val) {
      this.listQuery.checkstar = (val && val[0]) || "";
      this.listQuery.checkend = (val && val[1] + " 23:59:59") || "";
    },
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  // justify-content: space-around;
  .wc_1-one{
    width: 150px;
    text-align: right;
    padding: 0 30px;
  }
}
</style>