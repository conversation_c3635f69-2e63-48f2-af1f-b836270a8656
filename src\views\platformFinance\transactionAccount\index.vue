<template>
  <div class="tradeaccount-container">
    <div class="history-record">
      <el-button style="float: right" plain v-if="$store.getters.roles.indexOf('plataccountdail')>-1" @click="historyClick()">历史记录</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="tradeaccount"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="币种" prop="coinname" align="center" min-width="80"> </el-table-column>
      <el-table-column label="账户资产" prop="amount" align="center" min-width="110px">
          <template slot-scope="{row}">
            <span>{{row.amount || '--'}}</span>
          </template>
       </el-table-column>
      <el-table-column label="已实现盈亏" prop="assetaward" align="center" min-width="95px"> 
         <template slot-scope="{row}">
            <span>{{row.amount || '--'}}</span>
          </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getplataccount } from "@/api/platformFinance";
import clipboard from '@/directive/clipboard/index.js'
let QRCode = require("qrcode");
export default {
  name: "transactionAccount",
  data() {
    return {
      //表格加载中效果
      listLoading: false,
      tradeaccount: null,
    };
  },

  components: {},

  computed: {},
  directives: {
    clipboard
  },
  mounted() {
    this.getList();
  },

  methods: {
    //  点击历史记录
    historyClick() {
      this.$router.push({
        path: "/platformFinance/overView/transactionHistoryRecord",
      });
    },
    //数据
    getList() {
      this.listLoading = true;
      getplataccount({}).then((res) => {
        this.tradeaccount = res.data;
        this.listLoading = false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.tradeaccount-container{
  .history-record{
    height: 20px;
    margin-top: 10px;
  }
  .dialogc {
    padding-top: 22px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}
</style>