<template>
  <div class="rebate-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        placeholder="顶级代理ID/昵称"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        placeholder="上级代理ID/用户名"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.stype"
        placeholder="状态"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="(item, index) in stateOptions"
          :key="index"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin-left: 20px; margin-right: 5px; font-size: 12px">返佣时间</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change='filterTimeTransform'
      >
      </el-date-picker>
      <el-button
        class="filter-item"
        style="margin-left: 10px"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('getrebotlistexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        导出
      </el-button>
      <el-button
        class="filter-item"
        v-if="$store.getters.roles.indexOf('sendrebot')>-1"
        style="margin-left: 10px"
        size="mini"
        type="primary"
        :disabled="!multipleSelection.length"
        @click="oneClickSendrebot"
      >
        一键发放
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="rebateList"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
      @selection-change="handleSelectionChange">
    >
      <el-table-column :selectable="checkSelect" label-class-name="DisabledSelection" align="center" type="selection" width="70px"></el-table-column>
      <el-table-column label="UID" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column label="用户名" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户交易金额" min-width="115px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.trading_amount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户手续费" prop="trading_fee" min-width="105px" align="center"/>
      <el-table-column label="返佣金额" prop="rebate_commission" min-width="90px" align="center"/>
      <el-table-column label="去除返佣" prop="ignore_rebate_commission" min-width="90px" align="center"/>
      <el-table-column label="返佣比例" prop="agent_rebate_ratio" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.agent_rebate_ratio}}</span
          ><span>%</span>
        </template>
      </el-table-column>
      <el-table-column label="返佣期限" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.period }}</span
          ><span>天</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{ status[row.status_stype] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="统计时间" prop="end_period" align="center" width="75"/>
      <el-table-column label="发放时间" prop="send_time" align="center" width="75">
        <template slot-scope="{ row }">
          {{row.status_stype !== 1?row.send_time:'--'}}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="350px" v-if="$store.getters.roles.indexOf('sendrebot')>-1 || $store.getters.roles.indexOf('getuserrebot')>-1 || $store.getters.roles.indexOf('rebotswitchstatus')>-1">
        <template slot-scope="{ row }">
          <el-button size="mini" type="primary" :disabled="row.status_stype == 4" v-if="[1,4].indexOf(row.status_stype) > -1 && $store.getters.roles.indexOf('sendrebot')>-1" @click="sendrebotClick(row,3)">取消发放</el-button>
          <el-button size="mini" type="primary" :disabled="row.status_stype == 4" v-if="[1,4].indexOf(row.status_stype) > -1 && $store.getters.roles.indexOf('sendrebot')>-1" @click="sendrebotClick(row,1)">发放</el-button>
          <el-button size="mini" type="primary" v-if="[1,4].indexOf(row.status_stype) > -1 && $store.getters.roles.indexOf('rebotswitchstatus')>-1" @click="switchstatusClick(row,row.status_stype == 1?4:1)">{{row.status_stype == 1?'冻结':'恢复'}}发放</el-button>
          <el-button size="mini" type="primary" v-if="$store.getters.roles.indexOf('getuserrebot')>-1"  @click="DetailClick(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      :total="total"
      :page-sizes="[300,500,800,1000]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getrebotlist, sendrebot, getrebotlistexport, rebotswitchstatus } from "@/api/rebateQuery";

export default {
  name: "rebatelist",
  // components: { Page:Page },
  data() {
    return {
      listLoading: false,
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        star: "", //开始
        end: "", //结束 
        stype: null, // 0：全部 1待发放 2已发放
        pageNo: 1,
        pagesize: 300,
      },
      status: {
        1: '待发放',
        2: '已发放',
        3: '已取消',
        4: '已冻结',
      },
      stateOptions: [
        { key: 1, name: '待发放' },
        { key: 2, name: '已发放' },
        { key: 3, name: '已取消' },
        { key: 4, name: '已冻结' },
      ],
      rebateList: null,
      total: 0,
      multipleSelection: [],
      exportLoading: false, //导出加载中效果
    };
  },
  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    // 一键发放
    oneClickSendrebot(){
      if(this.multipleSelection.length){
        this.$confirm('确认一键发放？')
        .then(_ => {
          this.multipleSelection.forEach((v,i)=>{
            sendrebot({
              id: v.id,
              status: 1,
            }).then((res)=>{
              if(this.multipleSelection.length-1 === i){
                this.$notify({
                  title:'操作',
                  message: '操作成功',
                  type: "success",
                  duration: 2000,
                })
                this.getList()
              }
            })
          })
        })
        .catch(_ => {});
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    checkSelect (row,index) {
      let isChecked = true;
      if (row.status_stype === 1) { // 判断里面是否存在某个参数
        isChecked = true
      } else {
        isChecked = false
      }
      return isChecked
    },

    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      data.stype = data.stype || undefined
      getrebotlist(data).then((res) => {
        this.rebateList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    sendrebotClick(row,type){
      this.$confirm(`确认${type==3?'取消':''}发放？`)
      .then(_ => {
        sendrebot({
          id: row.id,
          status: type,
        }).then((res)=>{
          this.$notify({
            title:'操作',
            message: '操作成功',
            type: "success",
            duration: 2000,
          })
          this.getList()
        })
      })
      .catch(_ => {});
    },
    // 冻结功能
    switchstatusClick(row,type){
      this.$confirm(`确认将此用户${type==4?'冻结':'恢复'}发放？`)
      .then(_ => {
        rebotswitchstatus({
          id: row.id,
          status: type,
        }).then((res)=>{
          this.$notify({
            title:'操作',
            message: '操作成功',
            type: "success",
            duration: 2000,
          })
          this.getList()
        })
      })
      .catch(_ => {});
    },
    //跳转详情页
    DetailClick(row) {
      this.$router.push({
        path: "/rebateQuery/rebateListDetail",
        query: { 
          dt: JSON.stringify(row)
        },
      });
    },
    // 搜索事件
    handleFilter() {
      this.getList();
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      data.stype = data.stype || undefined
      getrebotlistexport(data).then((res) => {
        if(res.ret == 0){
            window.location.href=res.data.download_url;
            this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val && val[1]+' 23:59:59' || '';
    },
  },
};
</script>
<style lang="scss">
.rebate-container{
  /*表格表头全选*/
	.el-table .DisabledSelection .cell .el-checkbox__inner{
	  margin-left: -30px;
	  position:relative;
	}
	.el-table .DisabledSelection .cell:before{
	  content:"全选";
	  position:absolute;
	  right:11px;
	}
}

</style>
<style lang="scss" scoped>
</style>