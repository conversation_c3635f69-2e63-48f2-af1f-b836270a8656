<template>
  <div class="watchuserlist-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        style="margin-left: 20px; margin-top: 5px"
        class="filter-item"
        size="mini"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        style="margin-left: 20px; margin-top: 5px;float:right;"
        class="filter-item"
        size="mini"
        type="success"
        v-if="$store.getters.roles.indexOf('watchusermark') > -1"
        @click="handleAdd()"
      >
        添加嫌疑用户
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column label="UID" prop="user_id" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.user_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户名" prop="user_name" align="center">
      </el-table-column>
      <el-table-column label="用户类型" min-width="105px" align="center">
        <template slot-scope="{ row }">
          <span>{{
            (row.user_type && userTypeOptions[row.user_type]) || "--"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="KYC时间" prop="verify_time" align="center">
      </el-table-column>
      <el-table-column label="注册时间" prop="register_time" align="center">
      </el-table-column>
      <el-table-column
        label="最后登录时间"
        prop="last_login_time"
        align="center"
      >
      </el-table-column>
      <el-table-column label="最后登录IP" prop="last_login_ip" align="center">
      </el-table-column>
      <el-table-column label="标记观察次数" prop="watch_times" align="center">
      </el-table-column>
      <el-table-column label="添加时间" prop="created_time" align="center">
      </el-table-column>
      <el-table-column label="操作者" prop="operator" align="center">
      </el-table-column>
      <el-table-column label="备注" prop="comment" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.comment || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="280">
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            type="info"
            v-if="$store.getters.roles.indexOf('watchusermark') > -1"
            @click="handleClickCancel(row, 'ipuserlist')"
            >取消观察</el-button
          >
          <el-button
            size="mini"
            type="success"
            v-if="$store.getters.roles.indexOf('watchusermark') > -1"
            @click="handleClickComment(row, 'iptradelist')"
            >备注</el-button
          >
          <el-button
            size="mini"
            type="primary"
            @click="detailClick(row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200, 100, 50, 20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />

    <!-- handle -->
    <el-dialog
      v-dialogDrag
      :visible.sync="handleDialogShow"
      width="500px"
      title="备注"
      @closed="closedDialog"
    >
      <el-form
        ref="handleform"
        :model="formData"
        :rules="rules"
        label-position="left"
        label-width="auto"
        style="margin: 0 20px"
      >
        <el-form-item label="UID">
          <el-input
            placeholder="请输入UID"
            :disabled="formData.stype==1"
            v-model="formData.user_id"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder="请输入内容"
            maxlength="20"
            show-word-limit
            v-model="formData.comment"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogEntryClick">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
//引入封装接口
import {
  watchuserlist,
  watchusermark,
  watchuserunmark,
} from "@/api/riskAdminister";

export default {
  name: "watchuserlist",
  data() {
    return {
      total: 0,
      tableData: [],
      listLoading: true,
      filterTime: [],
      listQuery: {
        sname: "",
        pageNo: 1,
        pagesize: 20,
      },
      handleDialogShow: false, // 操作弹框
      formData: {
        user_id: "",
        comment: "",
        stype: 0,
      },
      rules: {
        user_id: [{ required: true, message: "请填写UID", trigger: "blur" }],
        comment: [{ required: true, message: "请填写备注", trigger: "blur" }],
      },
      
      userTypeOptions: {
        1: "顶级代理",
        2: "代理",
        3: "普通用户",
        4: "代理直推用户",
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    dialogEntryClick() {
      watchusermark(this.formData).then((res) => {
        this.$notify({
          title: "成功",
          message: "操作成功",
          type: "success",
          duration: 2000,
        });
        this.handleDialogShow = false
        this.getList();
      });
    },
    handleClickCancel(row) {
      this.$confirm(`确认取消观察此用户(${row.user_name})"？`)
        .then((_) => {
          watchuserunmark({
            user_id: JSON.stringify(row.user_id)
          }).then((res) => {
            this.$notify({
              title: "操作",
              message: "操作成功",
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        })
        .catch((_) => {});
    },
    handleClickComment(row) {
      this.handleDialogShow = true;
      this.formData = {
        user_id: JSON.stringify(row.user_id),
        comment: row.comment,
        stype: 1,
      }
    },
    // 弹框关闭
    closedDialog() {
      this.formData = {
        user_id: "",
        comment: "",
        stype: 0,
      };
    },
    // 添加嫌疑用户
    handleAdd() {
      this.handleDialogShow = true;
    },
    //跳转详情页
    detailClick(row) {
      this.$router.push({
        path: "/user/detail",
        query: {
          id: row.user_id,
        },
      });
    },
    // 回车搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //  table列表数据
    getList() {
      //开始有加载中效果
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery);
      watchuserlist(data).then((res) => {
        if (res.data) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        } else {
          this.tableData = [];
          this.total = 0;
        }
        this.listLoading = false;
      });
    },
    dataTimeChange(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = (val && val[1]) || "";
    },
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 0 10px;

  // justify-content: space-around;
  .wc_1-one {
    width: 45%;
  }
  .idPhotoImg_wrap ::v-deep .el-image__inner {
    width: auto;
  }
  .idPhotoImg_wrap ::v-deep .el-image__error {
    min-width: 100px;
  }
}
</style>