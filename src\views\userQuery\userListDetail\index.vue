<template>
  <div class="userlistdetail-container">
    <el-row
      :gutter="6"
      style="border: 1px solid #f0f2f5; width: 99.5%; margin: 20px auto"
    >
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content">
          <span class="grid-content-left">UID:</span>
          <span class="grid-content-right">{{ ListDetail.user_id }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">手机号:</span>
          <span class="grid-content-right">{{ ListDetail.phone || '--' }}</span>
          <el-button @click="reset(1)" v-if="ListDetail.phone && ListDetail.email && $store.getters.roles.indexOf('resetuserphone')>-1" size="mini" style="margin-left: 15px;" type="primary">重置</el-button>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">谷歌验证:</span>
          <span v-show="Number(ListDetail.totp_secret) != 0" class="grid-content-right" style="margin-top:10px">******</span>
          <span v-show="Number(ListDetail.totp_secret) == 0" class="grid-content-right">--</span>
          <el-button @click="reset(4)" v-if="ListDetail.totp_secret && Number(ListDetail.totp_secret) != 0 && $store.getters.roles.indexOf('resetuserphone')>-1" size="mini" style="margin-left: 15px;" type="primary">重置</el-button>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">用户类型:</span>
          <span class="grid-content-right">{{ userTypeOptions[ListDetail.user_type] }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">注册时间:</span>
          <span class="grid-content-right">{{ ListDetail.created_time }}</span>
        </div>
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content">
          <span class="grid-content-left">用户名:</span>
          <span class="grid-content-right">{{ ListDetail.user_name }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">邮箱:</span>
          <span class="grid-content-right">{{ ListDetail.email || '--' }}</span>
          <el-button @click="reset(2)" v-if="ListDetail.phone && ListDetail.email && $store.getters.roles.indexOf('resetuserphone')>-1" size="mini" style="margin-left: 15px;" type="primary">重置</el-button>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">资金密码:</span>
          <span v-show="Number(ListDetail.fund_passwd) != 0" class="grid-content-right" style="margin-top:10px">{{ ListDetail.fund_passwd ? '******' : '--' }}</span>
          <span v-show="Number(ListDetail.fund_passwd) == 0" class="grid-content-right">--</span>
          <el-button @click="reset(3)" v-if="ListDetail.fund_passwd && Number(ListDetail.fund_passwd) != 0 && $store.getters.roles.indexOf('resetuserphone')>-1" size="mini" style="margin-left: 15px;" type="primary">重置</el-button>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">邀请码:</span>
          <span class="grid-content-right">{{ ListDetail.invite_code || '--' }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">最后登录时间:</span>
          <span class="grid-content-right">{{
            ListDetail.last_login_time
          }}</span>
        </div>
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content"></div>
        <div class="grid-content"></div>
        <div class="grid-content"></div>
        <div class="grid-content"></div>
        <div class="grid-content">
          <span class="grid-content-left">最后登录IP:</span>
          <span class="grid-content-right">{{ ListDetail.last_login_ip }}</span>
        </div>
      </el-col>
    </el-row>

    <el-row
      :gutter="6"
      style="border: 1px solid #f0f2f5; width: 99.5%; margin: 20px auto"
    >
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content">
          <span class="grid-content-left">顶级代理ID:</span>
          <span class="grid-content-right">{{ ListDetail.topagent || '--' }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">下级代理人数:</span>
          <span class="grid-content-right">{{ ListDetail.is_agent?ListDetail.childagent:'--' }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">手续费返佣比例:</span>
          <span class="grid-content-right">{{
            ListDetail.is_agent?ListDetail.agent_rebate_ratio:'--'
          }}</span>
        </div>
        <!-- <div class="grid-content">
          <span class="grid-content-left">修改内容:</span>
          <span class="grid-content-right">{{
            ListDetail.modify_content
          }}</span>
        </div> -->
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content">
          <span class="grid-content-left">上级代理ID:</span>
          <span class="grid-content-right">{{ ListDetail.paregant || '--' }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">邀请人数(含间接邀请):</span>
          <span class="grid-content-right">{{ ListDetail.is_agent?ListDetail.childcount:'--' }}</span>
        </div>
       <!-- <div class="grid-content">
          <span class="grid-content-left">最后修改时间:</span>
          <span class="grid-content-right">{{
            ListDetail.is_agent?ListDetail.up_agent_time:'--'
          }}</span>
        </div> -->
        <!-- <div class="grid-content">
          <span class="grid-content-left">操作人:</span>
          <span class="grid-content-right">{{ ListDetail.Operator }}</span>
        </div> -->
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content"></div>
        <div class="grid-content"></div>
      </el-col>
    </el-row>

    <el-row
      :gutter="6"
      style="border: 1px solid #f0f2f5; width: 99.5%; margin: 20px auto"
    >
      <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
        <div class="grid-content" v-if="ListDetail.state == 3 || ListDetail.state == 6">
          <span class="grid-content-left">身份信息:</span>
          <span class="grid-content-right">{{ ListDetail.real_name || '--' }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">USDT钱包地址: </span>
          <span class="grid-content-right">{{ ListDetail.address || '--' }}</span>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
        <div class="grid-content" v-if="ListDetail.state == 3 || ListDetail.state == 6">
          <span class="grid-content-left">通过时间:</span>
          <span class="grid-content-right">{{ ListDetail.last_submit || '--' }}</span>
        </div>
        <div class="grid-content"></div>
      </el-col>
    </el-row>

    <el-row
      :gutter="6"
      style="border: 1px solid #f0f2f5; width: 99.5%; margin: 20px auto"
    >
      <el-col :xs="3" :sm="3" :md="3" :lg="3" :xl="3">
        <div class="grid-content" style="font-size: 14px">收款方式</div>
      </el-col>
      <el-col :xs="21" :sm="21" :md="21" :lg="21" :xl="21">
        <div v-show="payInfo.length>0" v-for="(ite,index) in payInfo" :key="index" class="grid-content">
          <span style="margin-right: 30px; font-size: 14px;">{{['','银行卡','支付宝','微信'][ite.type]}}</span>
          <span v-show="ite.type == 1" style="font-size: 14px; margin-right:10px;">开户银行:</span>
          <span v-show="ite.type == 1" class="grid-content-right;" style="margin-right: 30px;">{{ ite.bank_name }}</span>
          <span v-show="ite.type == 1" style="font-size: 14px; margin-right:10px;">开户支行:</span>
          <span v-show="ite.type == 1" class="grid-content-right" style="margin-right: 30px;">{{ ite.bank_branch_name || '--' }}</span>
          <span v-show="ite.type != 1" style="font-size: 14px; margin-right:10px;">收款码:</span>
          <span v-show="ite.type != 1" class="grid-content-right" style="margin-right: 30px;">
            <el-image 
              style="width: 32px; height: 32px"
              :src="ite.ocr_address" 
              :preview-src-list="[ite.ocr_address]">
            </el-image>
          </span>
          <span style="font-size: 14px; margin-right:10px;">{{['','银行卡号:','支付宝账号:','微信账号:'][ite.type]}}</span>
          <span class="grid-content-right">{{ ite.bank_numb }}</span>
        </div>
        <span v-show="payInfo.length==0" class="grid-content">--</span>
      </el-col>
    </el-row>

    <!-- <el-row
      :gutter="6"
      style="border: 1px solid #f0f2f5; width: 99.5%; margin: 20px auto"
    >
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="grid-content">
          <span class="grid-content-left">备注:</span>
          <span class="grid-content-right">{{ ListDetail.content }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">标签:</span>
          <span class="grid-content-right">{{ ListDetail.label }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">其他操作:</span>
          <span class="grid-content-right"></span>
        </div>
      </el-col>
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="grid-content">
          <span class="grid-content-left">操作人:</span>
          <span class="grid-content-right"></span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">操作人:</span>
          <span class="grid-content-right"></span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">操作人:</span>
          <span class="grid-content-right"></span>
        </div>
      </el-col>
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="grid-content">
          <span class="grid-content-left">操作时间:</span>
          <span class="grid-content-right">{{ ListDetail.display_time }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">操作时间:</span>
          <span class="grid-content-right">{{ ListDetail.display_time }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">操作时间:</span>
          <span class="grid-content-right">{{ ListDetail.display_time }}</span>
        </div>
      </el-col>
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="grid-content">
        </div>
        <div class="grid-content">
          <el-button size="mini">删除</el-button>
        </div>
        <div class="grid-content">
          
        </div>
      </el-col>
    </el-row> -->

      <div class="grid-content">
          <span class="grid-content-left">资产</span>
        </div>
    <el-row :gutter="6" style="width: 99.5%; margin: 30px auto">
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content">
          <span class="font">钱包账户资产(USDT):</span>
        </div>
        <div class="grid-content" style="border-bottom: 1px solid #f0f2f5">
          <span>{{ ListDetail.wallettotal }}</span>
        </div>
        <el-row>
          <el-col :span="12">
            <div class="grid-content">
              <span class="font">可用余额</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.walletbalance }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="grid-content">
              <span class="font">冻结数量</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.walletlock }}</span>
            </div>
          </el-col>
        </el-row>
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8"
        style="border-left: 1px solid #f0f2f5"
      >
        <div class="grid-content">
          <span class="font">合约账户资产(USDT):</span>
        </div>
        <div class="grid-content" style="border-bottom: 1px solid #f0f2f5">
          <span>{{ ListDetail.accbalance }}</span>
        </div>
        <el-row>
          <el-col :span="12">
            <div class="grid-content">
              <span class="font">账户权益</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.accequity }}</span>
            </div>
            <div class="grid-content">
              <span class="font">冻结保证金</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.accbond }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="grid-content">
              <span class="font">可用余额</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.accavailable }}</span>
            </div>
            <div class="grid-content">
              <span class="font">未实现盈亏</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.accprofit }}</span>
            </div>
          </el-col>
        </el-row>
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8"
        style="border-left: 1px solid #f0f2f5"
      >
        <div class="grid-content">
          <span class="font">跟单账户资产(USDT):</span>
        </div>
        <div class="grid-content" style="border-bottom: 1px solid #f0f2f5">
          <span>{{ ListDetail.followbalance }}</span>
        </div>
        <el-row>
          <el-col :span="12">
            <div class="grid-content">
              <span class="font">账户权益</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.followequity }}</span>
            </div>
            <div class="grid-content">
              <span class="font">冻结保证金</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.followbond }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="grid-content">
              <span class="font">可用余额</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.followavailable }}</span>
            </div>
            <div class="grid-content">
              <span class="font">未实现盈亏</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.followprofit }}</span>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>
//时间的转换
import { parseTime } from "@/utils";
const userTypeOptions = {
  1: "顶级代理",
  2: "代理",
  3: "普通用户",
  4:'代理直推用户'
};
import { userinfo, userpayments, resetuserphone } from "@/api/userQuery";
export default {
  name: "userListDetail",
  data() {
    return {
      ListDetail: {
        user_id: '--'
      },
      payInfo: [],
      //用户类型
      userTypeOptions,
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getDetails()
    userpayments({ user_id:JSON.parse(this.$route.query.id)}).then((data) => {
      this.payInfo = data.data;
    })
  },

  methods: {
    getDetails(){
      userinfo({ user_id:JSON.parse(this.$route.query.id)}).then((data) => {
        this.ListDetail = data.data;
      });
    },
    reset(type){
      let obj = {
        1: '手机号',
        2: '邮箱',
        3: '资金密码',
        4: '谷歌验证码',
      }
      this.$confirm('确认重置当前用户的'+obj[type], '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          resetuserphone({ uid :JSON.parse(this.$route.query.id)+'', stype: type}).then((data) => {
            this.$notify({
              title: "成功",
              message: "操作成功",
              type: "success",
              duration: 2000,
            });
            this.getDetails()
          })
        }).catch(() => {
          // this.$notify({
          //   type: 'info',
          //   message: '已取消删除'
          // });          
        });
    }
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 99.7%;
  height: 100px;
  // background: firebrick;
  border: 1px solid #f0f2f5;
  margin: 20px auto;
  display: flex;
  flex-wrap: wrap;
  // flex-direction: column;
  align-items: center;
  // justify-content: space-around;

  .wc_1-one {
    width: 45%;
    // background: firebrick;
    padding-left: 10%;
    box-sizing: border-box;
  }
}
.grid-content {
  min-height: 40px;
  display: flex;
  padding-left: 15px;
  align-items: center;
  
  .grid-content-right {
     word-wrap: break-word;
     word-break: normal;
  }
  .grid-content-left {
    width: 150px;
    font-size: 14px;
  }
}
.font {
  font-size: 14px;
  color: #999;
}

</style>