<template>
  <div class="operating-container">
    <el-table
      v-loading="listLoading"
      :data="operatingList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column
        label="姓名"
        prop="relname"
        align="center"
        min-width="110px"
      ></el-table-column>
      <el-table-column
        label="手机号"
        prop="mobile"
        align="center"
        min-width="110px"
      >
      </el-table-column>
      <el-table-column
        label="邮箱"
        prop="email"
        align="center"
        min-width="110px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.email || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        prop="content"
        align="center"
        min-width="110px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.content || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="提交时间"
        prop="creat_time"
        align="center"
        min-width="110px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.creat_time || "--" }}</span>
        </template>
      </el-table-column>
       <el-table-column
        label="IP地址"
        prop="ip_address"
        align="center"
        min-width="110px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.ip_address || "--" }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getcontactuslist } from "@/api/systemAdminister";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "contactUs",
  data() {
    return {
      listLoading: false,
      operatingList: null,
      total: 0,
      filterTime: [],
      listQuery: {
        uid: "", //用户id
        star: "",
        end: "",
        pageNo: 1,
        pagesize: 10,
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    //渲染table数据
    getList(){
      this.listLoading = true;
      var data = {
          pageNo:this.listQuery.pageNo,
          pagesize:this.listQuery.pagesize
      };
      getcontactuslist(data).then((response) => {
        this.operatingList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>

</style>