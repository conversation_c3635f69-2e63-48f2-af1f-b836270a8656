<template>
  <div class="important_container">
    <div class="filter-container">
      <el-select
        size="mini"
        v-model="listQuery.lang_type"
        style="width: 120px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in langOptions"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px; margin-top: 10px"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        style="margin-top: 10px; margin-left: 10px; float: right"
        class="filter-item"
        size="mini"
        type="success"
        @click="handleAdd"
        v-if="$store.getters.roles.indexOf('noticeimportantadd') > -1"
      >
        添加首页浮层
      </el-button>
      <!-- v-if="$store.getters.roles.indexOf('') > -1" -->
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 10px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column
        label="标题"
        prop="title"
        align="center"
        min-width="78"
      ></el-table-column>
      <el-table-column label="内容" align="center" min-width="90">
        <template slot-scope="{ row }">
          <el-image
            v-if="row.content && row.notice_type == 2"
            :src="row.content"
            style="width: 50px"
            fit="cover"
            :preview-src-list="row.imgList"
          ></el-image>
          <span v-if="row.notice_type == 1">{{ row.content }}</span>
        </template>
      </el-table-column>
      <el-table-column label="链接" prop="pnl" align="center" min-width="125">
        <template slot-scope="{ row }">
          <span>{{ row.link || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        prop="create_time"
        align="center"
        min-width="125"
      >
      </el-table-column>
      <el-table-column
        label="下架时间"
        prop="end_time"
        align="center"
        min-width="125"
      >
      </el-table-column>
      <el-table-column
        label="操作"
        prop="agent_rebate_ratio"
        align="center"
        min-width="180"
        v-if="
          $store.getters.roles.indexOf('noticeimportantadd') > -1 ||
          $store.getters.roles.indexOf('noticeimportantdel') > -1 ||
          $store.getters.roles.indexOf('noticeimportantvalid') > -1
        "
      >
        <template slot-scope="{ row }">
          <el-button
            v-if="$store.getters.roles.indexOf('noticeimportantadd') > -1"
            type="primary"
            size="mini"
            @click="handleUpd(row)"
            >修改</el-button
          ><el-button
            v-if="$store.getters.roles.indexOf('noticeimportantdel') > -1"
            type="danger"
            size="mini"
            @click="handleDel(row)"
            >删除</el-button
          >
          <el-button
            v-if="$store.getters.roles.indexOf('noticeimportantvalid') > -1"
            :type="row.valid ? 'info' : 'success'"
            size="mini"
            @click="handleVld(row)"
            >{{ row.valid ? "下" : "上" }}架</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200, 100, 50, 20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <el-dialog
      title="添加首页浮层"
      :visible.sync="dialogVisible"
      width="483px"
      height="320px"
      :before-close="handleDialogClose"
    >
      <div class="dialogc">
        <div>
          <el-form
            :rules="rules"
            ref="ruleFormmr"
            style="margin: 0 10px"
            :model="ruleForm"
            label-width="90px"
            label-position="left"
          >
            <div class="divliang">
              <el-form-item label="标题" prop="title">
                <el-input
                  v-model="ruleForm.title"
                  placeholder="请输入内容"
                ></el-input>
              </el-form-item>
              <el-form-item label="语言" prop="lang_type">
                <el-select
                  v-model="ruleForm.lang_type"
                  placeholder="请选择语言类型"
                >
                  <el-option
                    v-for="item in langOptions"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="内容" required>
                <el-radio-group
                  @change="noticeTypeChange"
                  v-model="ruleForm.notice_type"
                >
                  <el-radio :label="1">重要公告</el-radio>
                  <el-radio :label="2">图片</el-radio>
                </el-radio-group>
                <el-row>
                  <el-form-item
                    v-show="ruleForm.notice_type == 1"
                    prop="content"
                  >
                    <el-input
                      type="textarea"
                      :rows="4"
                      placeholder="请输入内容"
                      v-model="ruleForm.content"
                      ref="contentInput"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item
                    v-show="ruleForm.notice_type == 2"
                    prop="content"
                  >
                    <div class="uploaddisplay">
                      <el-upload
                        class="avatar-uploader"
                        :action="actionUrl"
                        accept="image/png"
                        drag
                        :data="uploadData"
                        :before-upload="beforeAvatarUpload"
                        :on-error="uploadError"
                        ref="uploadzh"
                        :on-success="uploadSuccessZh"
                        :show-file-list="false"
                      >
                        <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                      </el-upload>
                      <div style="margin-left: 10px; line-height: 20px">
                        图片尺寸要求：640 x 700 px
                      </div>
                    </div>
                  </el-form-item>
                </el-row>
              </el-form-item>
              <el-form-item label="跳转链接" prop="link">
                <el-input
                  class="w156"
                  placeholder="请输入链接地址"
                  v-model="ruleForm.link"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="下架时间" required>
                <el-col :span="11">
                  <el-form-item prop="end_time1">
                    <el-date-picker
                      type="date"
                      placeholder="选择日期"
                      v-model="ruleForm.end_time1"
                      style="width: 100%"
                      value-format="yyyy-MM-dd"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col class="line" :span="2">-</el-col>
                <el-col :span="11">
                  <el-form-item prop="end_time2">
                    <el-time-picker
                      placeholder="选择时间"
                      v-model="ruleForm.end_time2"
                      style="width: 100%"
                      value-format="HH:mm:ss"
                    ></el-time-picker>
                  </el-form-item>
                </el-col>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleDialogClose">取 消</el-button>
        <el-button size="small" type="primary" @click="handleDialogOkClick"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
// 封装api
import {
  getnoticeimportant,
  noticeimportantadd,
  noticeimportantvalid,
  noticeimportantdel,
} from "@/api/announcement";
let that;
export default {
  name: "importantNotice",
  data() {
    let validate_content = (rule, value, callback) => {
      if (!value) {
        return callback(
          new Error(
            this.ruleForm.notice_type == 1 ? "请输入内容" : "请上传图片"
          )
        );
      } else {
        callback();
      }
    };
    let validate_http = (rule, value, callback) => {
      if (value === "") {
        callback();
      }
      let reg =
        /(http|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/;
      if (!reg.test(value)) {
        return callback(new Error("链接格式不正确"));
      } else {
        callback();
      }
    };
    return {
      imgHostUrl: "",
      listLoading: false,
      total: 0,
      tableData: null,
      listQuery: {
        lang_type: "0",
        pageNo: 1,
        pagesize: 20,
      },
      exportLoading: false, //导出加载中效果
      langOptions: [
        { name: "中文简体公告", value: "0" },
        { name: "英文公告", value: "1" },
        { name: "中文繁体公告", value: "2" },
        { name: "韩文公告", value: "3" },
        { name: "日文公告", value: "4" },
      ],
      dialogVisible: false,
      uploadData: {},
      actionUrl: "",
      imageUrl: "",
      uploading: false,
      ruleForm: {
        id: 0,
        title: "",
        lang_type: "0",
        notice_type: 1,
        content: "",
        link: "",
        start_time: "",
        end_time1: "",
        end_time2: "",
      },
      rules: {
        title: [
          { required: true, message: "请输入标题", trigger: "blur" },
          { max: 10, message: "长度在 10 个字符以内", trigger: "blur" },
        ],
        link: [{ validator: validate_http, trigger: "blur" }],
        lang_type: [
          { required: true, message: "请选择语言类型", trigger: "change" },
        ],
        content: [{ validator: validate_content, trigger: "blur" }],
        end_time1: [
          {
            type: "string",
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        end_time2: [
          {
            type: "string",
            required: true,
            message: "请选择时间",
            trigger: "change",
          },
        ],
      },
    };
  },
  components: {},

  computed: {},

  mounted() {
    this.actionUrl =
      (process.env.VUE_APP_API == "/"
        ? window.location.protocol + "//" + window.location.host
        : process.env.VUE_APP_API) + "/managers/v1/banner/bcprobannerupload";
    this.getList();
    that = this;
  },

  methods: {
    noticeTypeChange() {
      this.ruleForm.content = "";
      this.imageUrl = "";
      this.$refs.ruleFormmr.clearValidate("content");
    },
    handleDialogOkClick() {
      this.$refs["ruleFormmr"].validate((valid) => {
        if (valid) {
          let end_time = this.ruleForm.end_time1 + " " + this.ruleForm.end_time2
          if(new Date(end_time).getTime()<=new Date().getTime()){
            this.$notify({
              title: "提示",
              message: "请输入正确的下架时间",
              type: "warning",
            });
            return false
          }
          let bb = this.ruleForm.content
          let imgName = bb.split('/')[bb.split('/').length-1]
          let content = this.ruleForm.notice_type == 2?imgName:bb
          var data = {
            id: this.ruleForm.id, //  (默认传0)，
            title: this.ruleForm.title, //  标题，
            content: content, //  内容，
            link: this.ruleForm.link || undefined, //  跳转链接，
            lang_type: Number(this.ruleForm.lang_type),
            notice_type: this.ruleForm.notice_type, //  通知类型 1文本 2图片，
            start_time: new Date().dateHandle(),
            end_time,
          };
          noticeimportantadd(data).then((res) => {
            this.$notify({
              title: "成功",
              message: "操作成功",
              type: "success",
            });
            this.getList();
            this.dialogVisible = false;
            this.$refs["ruleFormmr"].resetFields();
          });
        }
      });
    },
    uploadSuccessZh(response, file, fileList) {
      this.uploading = false;
      if (response.ret === 0) {
        that.ruleForm.content = response.data;
        this.$refs.ruleFormmr.clearValidate("content");
        var reader = new FileReader();
        reader.readAsDataURL(file.raw);
        reader.onload = function (event) {
          that.imageUrl = event.target.result;
        };
      } else {
        this.ruleForm.content = "";
        this.imageUrl = "";
        this.$notify({
          title: "失败",
          message: "中文BANNER上传失败，请重新尝试",
          type: "error",
        });
      }
    },
    uploadError(error, file, fileList) {
      this.$refs.uploadzh.clearFiles();
      this.uploading = false;
      this.$notify({
        title: "失败",
        message: "BANNER上传失败，请重新尝试",
        type: "error",
      });
    },
    beforeAvatarUpload(file) {
      let _this = this;
      let imgWidth = "";
      let imgHight = "";
      const isSize = new Promise(function (resolve, reject) {
        let width = 640;
        let height = 700;
        let _URL = window.URL || window.webkitURL;
        let img = new Image();
        img.onload = function () {
          imgWidth = img.width;
          imgHight = img.height;
          let valid = img.width == width && img.height == height;
          valid ? resolve() : reject();
        };
        img.src = _URL.createObjectURL(file);
      }).then(
        () => {
          let time = parseInt(new Date().getTime() / 1000) + "";
          let sin = md5(md5(process.env.VUE_APP_APIKEY) + time);
          this.uploadData["sign"] = sin;
          this.uploadData["ts"] = time;
          return file;
        },
        () => {
          _this.$notify({
            title: "失败",
            message:
              "上传文件的图片大小不合符标准,宽需要为640px，高需要为700px。当前上传图片的宽高分别为：" +
              imgWidth +
              "px和" +
              imgHight +
              "px",
            type: "error",
          });
          return Promise.reject();
        }
      );
      // return isPNG && isLt700K && isSize;
      return isSize;
    },
    handleDialogClose() {
      this.$refs.uploadzh.clearFiles();
      this.$refs["ruleFormmr"].resetFields();
      this.dialogVisible = false;
    },
    handleAdd() {
      (this.ruleForm = {
        id: 0,
        title: "",
        lang_type: "0",
        notice_type: 1,
        content: "",
        link: "",
        start_time: "",
        end_time1: "",
        end_time2: "",
      }),
        (this.dialogVisible = true);
    },
    handleUpd(row) {
      Object.assign(this.ruleForm, row);
      if (row.notice_type == 2) {
        this.imageUrl = row.content;
      }
      this.dialogVisible = true;
    },
    handleDel(row) {
      this.$confirm(`是否要删除标题为:${row.title}的重要公告？`, "提示", {
        confirmButtonText: "确定删除",
        cancelButtonText: "取消",
        type: "error",
      })
        .then(() => {
          noticeimportantdel({ id: row.id }).then((res) => {
            this.$notify({
              title: "成功",
              message: "操作成功",
              type: "success",
            });
            this.getList();
          });
        })
        .catch(() => {});
    },
    handleVld(row) {
      this.$confirm(
        `是否要${row.valid ? "下架" : "上架"}标题为:${row.title}的重要公告？`,
        "提示",
        {
          confirmButtonText: `确定${row.valid ? "下架" : "上架"}`,
          cancelButtonText: "取消",
          type: row.valid ? "warning" : "success",
        }
      )
        .then(() => {
          noticeimportantvalid({ id: row.id, valid: row.valid ? 0 : 1 }).then(
            (res) => {
              this.$notify({
                title: "成功",
                message: "操作成功",
                type: "success",
              });
              this.getList();
            }
          );
        })
        .catch(() => {});
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      getnoticeimportant(
        Object.assign({}, this.listQuery, {
          lang_type: Number(this.listQuery.lang_type),
        })
      ).then((res) => {
        this.listLoading = false;
        this.total = res.data.total;
        if (res.data.list) {
          this.tableData = res.data.list.map((v) => {
            v.lang_type = String(v.lang_type);
            if (v.notice_type == 2) {
              v.imgList = [v.content];
              v.end_time1 = v.end_time.split(" ")[0];
              v.end_time2 = v.end_time.split(" ")[1];
            }
            return v;
          });
        } else {
          this.tableData = [];
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.important_container {
  .uploaddisplay {
    width: 100%;
    white-space: nowrap;
    & > div {
      display: flex;
    }
  }
  ::v-deep .el-upload {
    width: 100%;
    .el-upload-dragger {
      width: 100%;
      height: auto;
      min-height: 180px;
      display: flex;
      justify-content: center;
      align-items: center;
      .avatar-uploader-icon {
        font-size: 28px;
      }
    }
  }
  ::v-deep .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 100%;
  }
  .line {
    text-align: center;
  }
}
</style>