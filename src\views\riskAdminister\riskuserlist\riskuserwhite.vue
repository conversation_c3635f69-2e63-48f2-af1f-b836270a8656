<template>
  <div class="riskuserwhite_wrap">
    <div v-if="$store.getters.roles.indexOf('setwhitelist') > -1" class="filter-container">
      <div class="add_wrap">
        <span>UID：</span>
        <el-input
          size="mini"
          v-model="addData.user_id"
          placeholder="请输入内容"
          style="width: 150px"
          class="filter-item"
          clearable
          @keyup.enter.native="handleFilter"
        />
        <span style="padding-left: 15px;">备注：</span>
        <el-input
          size="mini"
          v-model="addData.remart"
          type="textarea"
          placeholder="请输入内容"
          style="width: 150px"
          class="filter-item"
          clearable
          @keyup.enter.native="handleFilter"
        />
        <el-button
          style="margin-left: 15px"
          class="filter-item"
          size="mini"
          type="success"
          @click="handleAddWhite"
          :disabled="!addData.user_id || !addData.remart"
        >
          添加
        </el-button>
      </div>
      <el-divider></el-divider>
    </div>
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.userid"
        placeholder="UID/手机/邮箱"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.groupid"
        placeholder="风控组别"
        clearable
        style="width: 150px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in grouplist"
          :key="item.id"
          :label="item.group_name"
          :value="item.id"
        />
      </el-select>
      <el-button
        style=" margin-left: 10px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>
    <el-tabs v-model="tabActive" @tab-click="handleTab">
      <el-tab-pane label="当前白名单用户" name="first"></el-tab-pane>
      <el-tab-pane label="历史白名单用户" name="second"></el-tab-pane>
    </el-tabs>
    <el-table
      v-loading="listLoading"
      :data="financiaList"
      border
      fit
      size="mini"
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column 
        label="UID" 
        prop="user_id" 
        align="center" 
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.user_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="用户名"
        prop="user_name"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.user_name || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="风控组别"
        prop="group_name"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.group_name || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="添加时间"
        prop="creat_time"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.creat_time || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="添加人"
        prop="add_manage"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.add_manage || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        prop="add_remart"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.add_remart || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$store.getters.roles.indexOf('setwhitelist') > -1 && tabActive == 'second'"
        label="删除时间"
        prop="del_time"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.del_time || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$store.getters.roles.indexOf('setwhitelist') > -1 && tabActive == 'second'"
        label="删除人"
        prop="del_manage"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.del_manage || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$store.getters.roles.indexOf('setwhitelist') > -1 && tabActive == 'second'"
        label="删除备注"
        prop="del_remart"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.del_remart || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$store.getters.roles.indexOf('setwhitelist') > -1 && tabActive == 'first'"
        label="操作"
        min-width="100"
        align="center"
      >
        <template 
        slot-scope="{ row }"
        v-if="$store.getters.roles.indexOf('setwhitelist') > -1 && tabActive == 'first'"        
        >
          <el-button type="primary" size="mini" @click="handleDel(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[300, 100, 50, 10]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  getwhitelist,
  getgrouplist,
  addristwhite,
  delristwhite,
} from "@/api/riskAdminister";

export default {
  name: "riskuserwhite",
  data() {
    return {
      tabActive: "first",
      listLoading: false,
      total: 0,
      financiaList: null,
      grouplist: [],
      listQuery: {
        userid: "", //用户id,手机号，邮箱
        groupid: "", 
        pageNo: 1,
        pagesize: 10,
      },
      addData: {
        user_id: '',
        remart: '',
        group_id: '',
      }
    };
  },

  components: {},

  computed: {},

  mounted() {
    getgrouplist({pageNo: 1,pagesize: 10000 }).then(res=>{
      this.grouplist = res.data.list
    })
    this.getList();
  },

  methods: {
    // 列表添加按钮
    handleAddWhite(){
      this.$confirm(`是否确认添加UID为：${this.addData.user_id}的用户?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let data = this.addData
          Object.assign(data, this.addData, {
            group_id: 0,
          })
          addristwhite(this.addData).then((res) => {
            this.$notify({
              title: "操作",
              message: "添加成功",
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        })
        .catch(() => {});
    },
    // 列表删除按钮
    handleDel(row) {
      this.$prompt(`是否确认删除UID为：${row.user_name}的用户?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: '请输入删除备注',
        type: "warning",
      })
        .then(({value}) => {
          delristwhite({ 
            remart: value,
            id: row.id, 
          }).then((res) => {
            this.$notify({
              title: "操作",
              message: "删除成功",
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        })
        .catch(() => {});
    },
    handleTab(tab, event) {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery,{
        userid: this.listQuery.userid || undefined,
        groupid: this.listQuery.groupid || undefined,
        stype: this.tabActive == 'first'?1:2
      });
      getwhitelist(data).then((response) => {
        this.financiaList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
        console.log(response.data)
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.riskuserwhite_wrap {
  padding-top: 20px;
  &::v-deep .el-tabs {
    margin-left: 20px;
    margin-top: 10px;
  }
  .filter-container{
    margin-top: 0;
  }
  .add_wrap{
    display: flex;
    align-items: flex-start;
    span{
      display: flex;
      margin-top: 10px;
    }
  }
}
</style>