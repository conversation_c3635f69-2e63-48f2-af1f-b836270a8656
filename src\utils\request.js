import axios from 'axios'
import { MessageBox, Message, Notification } from 'element-ui'
import store from '@/store'
import router from '@/router'
import { getToken } from '@/utils/auth'
import Cookies from 'js-cookie'


// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_API,
  // process.env.NODE_ENV === "production"
  // ? config.url
  // : "http://manage.officeqb.com", //请求总地址
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 60000, //超时时间
})
// console.log(process.env.VUE_APP_APIKEY)
// request interceptor
service.interceptors.request.use(
  reqData =>{
    // console.log(reqData)
      let time = parseInt(new Date().getTime() / 1000) + "";
      let sin = md5(
        md5(process.env.VUE_APP_APIKEY) + time
      );
      // console.log(process.env)
      reqData.data['sign'] = sin
      reqData.data['ts'] = time
      if (store.getters.token) {
        reqData.headers['token'] = store.getters.token
      }
    return reqData
  },
  error =>{
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)
// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data
    if (res.ret !== 0) {
      if (res.ret == 606) {
        store.dispatch("user/logout");
        router.push(`/login`);
        Notification.error({
          title: '错误',
          message: '用户在其它地方登陆'
        })
      } else {
        Notification.error({
          title: '错误',
          message: res.msg || '网络状态不好,请稍后再试'
        })
      }
      return Promise.reject(new Error(res.msg || '网络状态不好,请稍后再试'))
    } else {
      return res
    }
  },
  error => {
    console.log('报错了' + error) // for debug
    Notification.error({
      title: '错误',
      message: error.message || '网络状态不好,请稍后再试'
    })
    return Promise.reject(error)
  }
)

export default service
